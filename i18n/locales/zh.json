{"system": "人工智能图像生成器", "helloWorld": "你好，世界！", "Describe the image you want to generate...": "描述您想要生成的图像...", "appTitle": "人工智能图像创作", "copyright": "版权所有 © {year}，AI图像创作", "available": "可用于新项目", "notAvailable": "目前不可用", "blog": "博客", "copyLink": "复制链接", "minRead": "分钟阅读", "articleLinkCopied": "文章链接已复制到剪贴板", "clickToClose": "单击任意位置或按ESC关闭", "promptDetails": "提示详情", "generateWithPrompt": "生成此提示", "generateWithSettings": "使用这些设置生成", "preset": "预设", "style": "风格", "resolution": "决议", "addImage": "添加图片", "modelPreset": "模型/预设", "imageDimensions": "图像尺寸", "yourImage": "你的图片", "generate": "生成", "nav": {"aitool": "AI工具", "history": "历史", "api": "API", "login": "登录", "orders": "订单"}, "3D Render": "3D渲染", "Acrylic": "丙烯酸", "Anime General": "动漫综述", "Creative": "有创意的", "Dynamic": "动态", "Fashion": "时尚", "Game Concept": "游戏概念", "Graphic Design 3D": "三维图形设计", "Illustration": "插图", "None": "无", "Portrait": "肖像", "Portrait Cinematic": "肖像电影模式", "Portrait Fashion": "肖像时尚", "Ray Traced": "光线追踪", "Stock Photo": "库存照片", "Watercolor": "水彩画", "AI Image Generator": "人工智能图像生成器", "Generate AI images from text prompts with a magical particle transformation effect": "通过文本提示生成具有神奇粒子变换效果的AI图像", "Enter your prompt": "输入您的提示", "Generating...": "生成中...", "Generate Image": "生成图像", "Enter a prompt and click Generate Image to create an AI image": "输入提示并点击生成图像以创建AI图像", "Prompt:": "提示：", "Download": "下载", "How It Works": "工作原理", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "这个AI图像生成器使用基于粒子的变换效果来可视化创建过程。当您输入提示并点击'生成'时，系统会：", "Sends your prompt to an AI image generation API": "将您的提示发送到AI图像生成API", "Creates a particle system with thousands of tiny particles": "创建一个包含数千个微小粒子的粒子系统", "Transforms the random noise particles into the generated image": "将随机噪声粒子转换为生成的图像", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "粒子从随机噪声模式开始，然后平滑地转变为最终图像，创造出模拟AI创作过程的神奇效果。", "Transform": "转换", "Transforming...": "转换中...", "Initializing particles...": "初始化粒子...", "Loading image...": "加载图像...", "Creating particle system...": "创建粒子系统...", "Adding event listeners...": "添加事件监听器...", "Ready!": "准备就绪！", "AI Image Particle Effect": "AI图像粒子效果", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "BaseMagicImage组件的演示，将粒子转换为AI生成的图像", "Click anywhere or press ESC to close": "单击任意位置或按ESC关闭", "auth": {"login": "登录", "loginDescription": "登录您的账户以继续", "email": "电子邮件", "enterEmail": "输入您的电子邮件", "password": "密码", "enterPassword": "输入您的密码", "rememberMe": "记住我", "welcomeBack": "欢迎回来", "dontHaveAccount": "没有账户？", "signUp": "注册", "forgotPassword": "忘记密码？", "signupFailed": "注册失败", "signupFailedDescription": "注册过程中出现错误。请重试。", "bySigningIn": "通过登录，您同意我们的", "termsOfService": "服务条款", "signUpTitle": "注册", "signUpDescription": "创建账户以开始", "name": "姓名", "enterName": "输入您的姓名", "createAccount": "创建账户", "alreadyHaveAccount": "已有账户？", "bySigningUp": "通过注册，您同意我们的", "backToHome": "返回首页", "notVerifyAccount": "您的账户尚未验证。请验证您的账户以继续", "verifyAccount": "验证账户", "resendActivationEmail": "重新发送激活邮件", "accountRecovery": "账户恢复", "accountRecoveryTitle": "恢复您的账户", "accountRecoveryDescription": "输入您的邮箱以接收密码重置说明", "sendRecoveryEmail": "发送恢复邮件", "recoveryEmailSent": "恢复邮件已发送", "recoveryEmailSentDescription": "请检查您的邮箱以获取密码重置说明", "resetPassword": "重置密码", "resetPasswordTitle": "重置您的密码", "resetPasswordDescription": "输入您的新密码", "newPassword": "新密码", "confirmPassword": "确认密码", "enterNewPassword": "输入您的新密码", "enterConfirmPassword": "确认您的新密码", "passwordResetSuccess": "密码重置成功", "passwordResetSuccessDescription": "您的密码已成功重置。现在您可以使用新密码登录", "activateAccount": "激活账户", "activateAccountTitle": "激活您的账户", "activateAccountDescription": "您的账户正在激活中...", "accountActivated": "账户已激活", "accountActivatedDescription": "您的账户已成功激活。现在您可以登录", "activationFailed": "激活失败", "activationFailedDescription": "无法激活您的账户。请重试或联系支持", "backToLogin": "返回登录", "loginFailed": "登录失败", "loginWithGoogle": "使用Google登录", "google": "Google", "filter": "Filter"}, "validation": {"invalidEmail": "无效的电子邮件", "passwordMinLength": "密码必须至少8个字符", "nameRequired": "姓名是必需的", "required": "此字段为必填项", "passwordsDoNotMatch": "密码不匹配"}, "imageSelect": {"pleaseSelectImageFile": "请选择一个图像文件", "selectedImage": "已选择的图像", "removeImage": "删除图像"}, "pixelReveal": {"loading": "加载图像中...", "processing": "处理图像中...", "revealComplete": "图像显示完成"}, "SIGNIN_WRONG_EMAIL_PASSWORD": "邮箱或密码错误", "Try again": "重试", "aiToolMenu": {"imagen": "Imagen", "videoGen": "视频生成", "speechGen": "语音生成", "musicGen": "音乐生成", "imagen3": "Imagen 3", "imagen3Description": "生成高质量、详细的图像，具有准确的文本渲染功能，用于创意视觉内容。", "imagen4": "Imagen 4", "imagen4Description": "前所未有地表达您的想法——使用Imagen，创意无限。", "gemini2Flash": "Gemini 2.0 Flash", "gemini2FlashDescription": "Gemini 2.0 Flash是从文本提示生成图像的强大工具。", "veo2": "Veo 2", "veo2Description": "比以往更强的控制力、一致性和创造力。", "veo3": "Veo 3", "veo3Description": "视频与音频的结合。我们最新的视频生成模型，旨在为电影制作人和故事讲述者提供支持。", "gemini25Pro": "Gemini 2.5 Pro", "gemini25ProDescription": "最先进的文本转语音模型。", "gemini25Flash": "Gemini 2.5 Flash", "gemini25FlashDescription": "大规模处理（例如多个PDF）。\n需要思考的低延迟、高容量任务\n代理用例", "link": "链接", "linkDescription": "使用超强的NuxtLink。", "soon": "即将推出"}, "readArticle": "阅读文章", "switchToLightMode": "切换到明亮模式", "switchToDarkMode": "切换到深色模式", "profile": "个人资料", "buyCredits": {"checkout": "结账", "checkoutDescription": "确认您的订单，然后选择付款方式。", "orderDetail": "订单详情", "credits": "积分", "pricePerUnit": "单价", "totalCredits": "总积分", "totalPrice": "总价", "payment": "付款", "submit": "提交", "cancel": "取消"}, "pricing": {"title": "价格", "description": "为您的图像生成需求选择完美的计划", "comingSoon": "即将推出", "comingSoonDescription": "我们的定价计划正在最终确定中。请稍后回来查看更新。"}, "magicImageDemo": {"title": "AI图像粒子效果", "description": "演示BaseMagicImage组件将粒子转换为AI生成的图像", "image": "图像", "aboutTitle": "关于此组件", "aboutDescription": "BaseMagicImage组件使用Three.js创建粒子系统，可以在随机位置和AI生成的图像之间转换。粒子以旋涡和流动效果移动，创造神奇的转换。", "featuresTitle": "特性", "features": {"particleRendering": "基于粒子的图像渲染", "smoothTransitions": "随机粒子位置和图像形成之间的平滑过渡", "interactiveControls": "交互式相机控制（拖动旋转，滚动缩放）", "customizable": "可自定义粒子数量和动画持续时间", "automatic": "自动或手动转换触发"}, "howItWorksTitle": "工作原理", "howItWorksDescription": "该组件分析图像的像素并创建3D粒子系统，其中每个粒子代表一个像素。较亮的像素更靠近查看者，创造微妙的3D效果。粒子最初在3D空间中随机散布，然后在触发时动画形成图像。"}, "privacy": {"title": "隐私政策", "description": "了解我们如何保护您的隐私和处理您的数据", "informationWeCollect": "我们收集的信息", "informationWeCollectDescription": "我们收集您直接向我们提供的信息，例如当您创建帐户、使用我们的服务或联系我们寻求支持时。", "howWeUseInformation": "我们如何使用您的信息", "howWeUseInformationDescription": "我们使用收集的信息来提供、维护和改进我们的服务，处理交易，并与您沟通。", "informationSharing": "信息共享", "informationSharingDescription": "未经您的同意，我们不会出售、交易或以其他方式将您的个人信息转移给第三方，除非本政策中描述的情况。", "dataSecurity": "数据安全", "dataSecurityDescription": "我们实施适当的安全措施来保护您的个人信息免受未经授权的访问、更改、披露或破坏。", "contactUs": "联系我们", "contactUsDescription": "如果您对本隐私政策有任何疑问，请通过我们的支持渠道联系我们。"}, "terms": {"title": "服务条款", "description": "使用Imagen服务的条款和条件", "acceptanceOfTerms": "1. 接受条款", "acceptanceOfTermsDescription": "通过访问和使用Imagen服务，您接受并同意受本协议的条款和条件约束。", "useOfService": "2. 服务使用", "useOfServiceDescription": "您同意仅出于合法目的并根据这些服务条款使用我们的服务。", "userAccounts": "3. 用户账户", "userAccountsDescription": "您有责任维护您的账户和密码的机密性。", "intellectualProperty": "4. 知识产权", "intellectualPropertyDescription": "我们服务上可用的所有内容和材料均受知识产权保护。", "termination": "5. 终止", "terminationDescription": "我们可能会完全自行决定终止或暂停您的账户和对服务的访问。", "disclaimers": "6. 免责声明", "disclaimersDescription": "服务按\"现状\"提供，不提供任何形式的保证。", "contactUsTerms": "联系我们", "contactUsTermsDescription": "如果您对这些服务条款有任何疑问，请通过我们的支持渠道联系我们。"}, "appName": "GeminiGen.AI", "quickTopUp": "快速充值", "customTopUp": "自定义充值", "numberOfCredits": "积分数量", "paypal": "PayPal", "paypalDescription": "使用您的PayPal账户安全支付", "debitCreditCard": "借记卡或信用卡", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "使用加密货币支付", "cryptoDescription": "比特币、以太坊和其他加密货币", "profileMenu": {"guide": "指南", "logo": "标志", "settings": "设置", "components": "组件"}, "loadingMoreItems": "正在加载更多项目...", "promptLabel": "提示词：", "Describe the video you want to generate...": "描述您想生成的视频...", "cancel": "取消", "confirm": "确认", "imageStyles": {"selectImageStyle": "选择图像样式", "chooseStyle": "选择样式", "examples": "例子", "selectStyleToSeeExamples": "选择一种风格查看示例", "3d-render": {"description": "创建具有适当光照和材质的照片级逼真3D渲染图像"}, "acrylic": {"description": "活力的丙烯画风格，具有大胆的笔触和丰富的色彩。"}, "anime-general": {"description": "日本动漫和漫画风格，具有富有表现力的角色和详细的背景"}, "creative": {"description": "艺术创新风格，富有创意的诠释和独特的构图"}, "dynamic": {"description": "充满活力的风格，富有动感、戏剧性的角度以及强有力的构图"}, "fashion": {"description": "专业时尚摄影风格，结合优雅造型和奢华美学"}, "game-concept": {"description": "电子游戏概念艺术风格，具有详细的角色和环境"}, "graphic-design-3d": {"description": "现代3D图形设计具有简洁的线条和当代美学"}, "illustration": {"description": "传统插画风格，具有细致的艺术作品和艺术技巧。"}, "none": {"description": "未应用特定风格，允许自然的AI解读"}, "portrait": {"description": "专业的人像风格，注重面部特征和表情"}, "portrait-cinematic": {"description": "电影肖像风格，具有戏剧性的灯光和电影般的品质。"}, "portrait-fashion": {"description": "结合优雅与专业摄影技巧的时尚人像风格"}, "ray-traced": {"description": "高级光线追踪渲染与真实反射和照明"}, "stock-photo": {"description": "商业图库风格，干净而专业的外观"}, "watercolor": {"description": "柔和的水彩画风格，色彩流动且具有艺术画笔效果。"}}, "history": {"tabs": {"imagen": "图像", "video": "视频", "speech": "演讲", "music": "音乐", "history": "历史"}}, "videoExamples": "视频示例", "videoExamplesDescription": "探索这些视频示例及其提示和设置。点击任意“使用此提示”按钮即可将提示复制到您的输入字段。", "useThisPrompt": "使用此提示", "model": "模型", "duration": "持续时间", "videoTypeSelection": "选择视频类型", "videoTypes": {"selectVideoType": "选择视频类型", "searchPlaceholder": "搜索视频类型...", "chooseType": "选择类型", "examples": "例子", "selectTypeToSeeExamples": "选择一种类型以查看示例", "advertisingMarketing": {"title": "广告与营销", "description": "推广产品/服务，投放Facebook、TikTok、YouTube广告，介绍品牌或项目，优化视频营销制作成本"}, "socialMediaContent": {"title": "社交媒体内容（短视频内容）", "description": "TikTok短视频、<PERSON><PERSON>、Shorts、故事视频、评论、AI配音、将文本或博客转换为视频。"}, "personalEntertainment": {"title": "个人创意内容 / 娱乐", "description": "喜剧视频，恶搞，表情包，叙述视频，动画，粉丝创作内容，AI驱动的短片"}, "educationTraining": {"title": "教育与培训", "description": "教学视频，网络学习，课程，技能教程，知识分享，AI虚拟形象讲师的演示文稿"}, "corporateCommunication": {"title": "内部通信与企业事务", "description": "入职培训视频、流程指南、内部视频公告、快速AI驱动的员工培训"}, "personalizedVideos": {"title": "个性化视频", "description": "生日祝福、周年纪念视频、个性化客户视频（邮件营销、客户服务）、针对特定候选人的招聘视频"}, "timeCostSavings": {"title": "生产中的时间和成本节约", "description": "不需要演员、工作室、后期制作团队，非常适合个人/小团队快速制作内容，自动化视频制作工作流程。"}}, "notifications": {"title": "Notifications", "description": "Your recent notifications and updates", "totalCount": "{count} notifications", "markAllRead": "Mark all as read", "loadMore": "Load more", "close": "Close", "empty": {"title": "No notifications", "description": "You're all caught up! No new notifications to show."}, "error": {"title": "Error loading notifications"}, "types": {"default": {"title": "Notification", "description": "You have a new notification"}, "video_1": {"title": "视频生成中", "description": "视频生成正在等待处理"}, "video_2": {"title": "视频生成完成", "description": "视频已成功生成"}, "video_3": {"title": "视频生成失败", "description": "视频生成失败"}, "image_1": {"title": "图像生成中", "description": "图像生成正在等待处理"}, "image_2": {"title": "图像生成完成", "description": "图像已成功生成"}, "image_3": {"title": "图像生成失败", "description": "图像生成失败"}, "tts_history_1": {"title": "音频生成中", "description": "文本转语音正在等待处理"}, "tts_history_2": {"title": "音频生成完成", "description": "语音合成功能已成功生成音频"}, "tts_history_3": {"title": "音频生成失败", "description": "文本转语音生成失败"}, "voice_training_1": {"title": "语音训练待定", "description": "语音训练正在等待处理"}, "voice_training_2": {"title": "语音训练完成", "description": "自定义语音模型训练已成功完成。"}, "voice_training_3": {"title": "语音训练失败", "description": "语音训练失败"}, "music_1": {"title": "音乐生成中", "description": "音乐生成正在等待处理。"}, "music_2": {"title": "音乐生成完成", "description": "人工智能音乐已成功生成。"}, "music_3": {"title": "音乐生成失败", "description": "音乐生成失败"}, "speech_1": {"title": "语音生成待定", "description": "您的语音生成请求正在等待处理。"}, "speech_2": {"title": "语音生成完成", "description": "您的演讲已成功生成。"}, "speech_3": {"title": "语音生成失败", "description": "您的语音生成失败。请再试一次。"}}, "time": {"justNow": "刚才", "minutesAgo": "{minutes}分钟前", "hoursAgo": "{hours}小时前", "yesterday": "昨天"}, "status": {"processing": {"title": "处理中", "description": "您的请求正在处理中"}, "success": {"title": "已完成", "description": "成功完成"}, "failed": {"title": "失败", "description": "处理过程中发生错误"}, "warning": {"title": "警告", "description": "已完成，但有警告"}, "pending": {"title": "待处理", "description": "等待处理"}, "cancelled": {"title": "取消", "description": "请求已取消"}}}, "footer": {"privacy": "Privacy", "terms": "Terms", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "textToSpeechOpenAI": "Text To Speech OpenAI", "doctransGPT": "DoctransGPT", "nuxtUIOnDiscord": "Nuxt UI on Discord", "youtube": "Youtube"}, "historyPages": {"imagenDescription": "Browse your AI-generated images and artwork", "musicDescription": "Browse your AI-generated music and audio content", "speechDescription": "Browse your AI-generated speech and voice content", "videoDescription": "Browse your AI-generated videos and animations", "imagenBreadcrumb": "Imagen", "musicBreadcrumb": "Music", "speechBreadcrumb": "Speech", "videoBreadcrumb": "Video Generation", "endOfImagesHistory": "You've reached the end of the images history", "endOfMusicHistory": "You've reached the end of the music history", "endOfSpeechHistory": "You've reached the end of the speech history", "endOfVideoHistory": "You've reached the end of the video history", "noVideosFound": "No videos found", "noVideosFoundDescription": "Start generating videos to see them here.", "errorLoadingVideo": "Error Loading Video", "loadingVideoDetails": "Loading video details...", "videoDetails": "Video Details", "videoInformation": "Video Information", "videoNotFound": "The video you are looking for could not be found or loaded.", "backToLibrary": "回到图书馆", "aiContentLibraryTitle": "人工智能内容库", "aiContentLibraryDescription": "浏览和管理不同类别的AI生成内容"}, "profileSettings": {"emailNotifications": "Email Notifications", "marketingEmails": "Marketing Emails", "securityAlerts": "Security Alerts"}, "settings": "设置", "userMenu": {"profile": "个人资料", "buyCredits": "购买积分", "settings": "设置", "api": "API", "logout": "退出登录", "greeting": "你好，{name}"}, "formats": {"mp3": "MP3", "wav": "WAV"}, "channels": {"mono": "单声道", "stereo": "立体声"}, "options": {"allow": "允许", "dontAllow": "不允许", "voices": "语音", "pickVoice": "选择语音"}, "common": {"home": "家"}, "Describe the speech you want to generate...": "描述您想要生成的讲话内容...", "listenToSpeech": "收听演讲", "generateSimilar": "生成相似", "voice": "语音", "emotion": "情感", "speed": "速度", "speed_settings": "速度设置", "speed_value": "速度值", "speed_slider": "速度滑块", "apply": "申请", "speech_settings": "语音设置", "current_speed": "当前速度", "reset_defaults": "恢复默认设置", "outputFormat": "输出格式", "outputChannel": "输出通道", "selectVoice": "选择语音", "selectEmotion": "选择情感", "selectFormat": "选择格式", "selectChannel": "选择频道", "noVoicesAvailable": "没有可用的语音", "noEmotionsAvailable": "没有情感可用", "searchVoices": "搜索语音...", "searchEmotions": "搜索情感...", "noVoicesFound": "未找到语音", "noEmotionsFound": "未发现情感", "retry": "重试", "noAudioSample": "暂无音频样本可用", "Speech Generation Complete": "语音生成完成", "Your speech has been generated successfully": "您的演讲已成功生成。", "stripe": "Stripe", "stripeDescription": "通过Stripe安全支付", "orders": {"title": "订单历史记录", "description": "查看您的交易和支付历史", "orderId": "订单编号", "amount": "金额", "credits": "积分", "quantity": "数量", "platform": "平台", "externalId": "交易 ID", "status": {"completed": "已完成", "success": "成功", "paid": "已支付", "pending": "待处理", "processing": "处理", "failed": "失败", "cancelled": "已取消", "error": "错误"}, "empty": {"title": "尚未收到订单", "description": "您还没有下过订单。购买积分来开始使用我们的服务。", "action": "购买积分"}, "endOfList": "您已查看所有订单", "errors": {"fetchFailed": "加载订单历史失败。请重试。"}, "meta": {"title": "订单历史 - Imagen AI", "description": "在Imagen AI上查看您的交易和付款历史"}}, "demo": {"notifications": {"title": "通知类型与状态演示", "description": "不同通知类型与各种状态的示例", "statusLegend": "状态图例"}, "speechVoiceSelect": {"title": "语音选择演示", "description": "演示带有 modelValue 属性的可重用 BaseSpeechVoiceSelectModal 组件"}}, "models": {"imagen3": "图像3", "gemini2Flash": "双子座2.0闪光灯", "gemini25Flash": "双子座 2.5 闪光", "sunoAI": "<PERSON><PERSON>人工智能", "udio": "乌迪欧", "veo2": "我看见2", "veo3": "维奥3", "imagen4": "图像 4"}, "aspectRatio": "纵横比", "Image Reference": "图片参考", "personGeneration": {"dontAllow": "Don't Allow", "allowAdult": "Allow Adult", "allowAll": "Allow All"}, "safety_filter_level": "安全过滤级别", "used_credit": "已用信用额度", "Safety Filter": "安全过滤器", "safetyFilter": {"blockLowAndAbove": "下方阻挡和上方阻挡", "blockMediumAndAbove": "中阻及以上", "blockOnlyHigh": "仅阻止高位", "blockNone": "无阻挡"}, "historyFilter": {"all": "所有", "imagen": "影像", "videoGen": "视频生成", "speechGen": "语音生成"}, "Person Generation": "人物生成", "downloadImage": "下载图片", "noImageAvailable": "无可用图片", "enhancePrompt": "增强提示", "addImages": "添加图片", "generateVideo": "生成视频", "happy": "快乐", "sad": "悲伤", "angry": "愤怒", "excited": "兴奋", "laughing": "笑", "crying": "哭泣", "calm": "冷静", "serious": "严肃", "frustrated": "沮丧", "hopeful": "充满希望的", "narrative": "叙事", "kids' storytelling": "儿童讲故事", "audiobook": "有声书", "poetic": "诗意的", "mysterious": "神秘的", "inspirational": "鼓舞人心", "surprised": "惊讶", "confident": "自信", "romantic": "浪漫", "scared": "害怕", "trailer voice": "预告片解说声音", "advertising": "广告", "speech": "演讲", "documentary": "纪录片", "newsreader": "新闻播音员", "weather report": "天气预报", "game commentary": "游戏解说", "interactive": "互动", "customer support": "客户支持", "playful": "俏皮", "tired": "疲倦", "sarcastic": "讽刺的", "disgusted": "厌恶", "whispering": "耳语", "persuasive": "有说服力的", "nostalgic": "怀旧", "meditative": "冥想的", "announcement": "公告", "professional pitch": "专业推介", "casual": "休闲", "exciting trailer": "精彩预告片", "dramatic": "戏剧性", "corporate": "公司", "tech enthusiast": "科技爱好者", "youthful": "年轻", "calming reassurance": "镇静的安慰", "heroic": "英雄的", "festive": "节日的", "urgent": "紧急", "motivational": "励志", "friendly": "友好", "energetic": "精力充沛", "serene": "宁静", "bold": "大胆", "charming": "迷人", "monotone": "单调", "questioning": "质疑", "directive": "指令", "dreamy": "梦幻的", "epic": "史诗", "lyrical": "抒情的", "mystical": "神秘的", "melancholy": "忧郁", "cheerful": "快乐", "eerie": "怪异", "flirtatious": "挑逗的", "thoughtful": "深思熟虑", "cinematic": "电影般的", "humorous": "幽默的", "instructional": "教学", "conversational": "会话型", "apologetic": "抱歉的", "excuse-making": "找借口", "encouraging": "鼓励", "neutral": "中立", "authoritative": "权威的", "sarcastic cheerful": "讽刺而愉快", "reassuring": "令人安心", "formal": "正式的", "anguished": "痛苦的", "giggling": "咯咯笑", "exaggerated": "夸张的", "cold": "冷", "hot-tempered": "急躁", "grateful": "感激", "regretful": "遗憾", "provocative": "挑衅的", "triumphant": "凯旋", "vengeful": "复仇的", "heroic narration": "英雄叙事", "villainous": "邪恶的", "hypnotic": "催眠术", "desperate": "绝望", "lamenting": "悲叹", "celebratory": "庆祝的", "teasing": "戏弄", "exhausted": "筋疲力尽", "questioning suspicious": "质疑可疑事项", "optimistic": "乐观的", "bright, gentle voice, expressing excitement.": "明亮而温柔的声音，表达出兴奋之情。", "low, slow voice, conveying deep emotions.": "低沉缓慢的声音，传递着深刻的情感。", "sharp, exaggerated voice, expressing frustration.": "尖锐夸张的声音，表达了沮丧。", "fast, lively voice, full of enthusiasm.": "快速而充满活力的声音，充满热情。", "interrupted, joyful voice, interspersed with laughter.": "打断了的愉悦声音，夹杂着笑声。", "shaky, low voice, expressing pain.": "颤抖而低沉的声音，表达痛苦。", "gentle, steady voice, providing reassurance.": "温柔而稳定的声音，给予安慰。", "mature, clear voice, suitable for formal content.": "成熟、清晰的声音，适合正式内容。", "weary, slightly irritated voice.": "疲惫而略显恼怒的声音。", "bright voice, conveying positivity and hope.": "明亮的声音，传达积极和希望。", "natural, gentle voice with a slow rhythm.": "自然温和的声音，节奏缓慢。", "lively, engaging voice, captivating for children.": "活泼、有趣的声音，吸引孩子们。", "even, slow voice, emphasizing content meaning.": "平稳缓慢的声音，强调内容的意义。", "rhythmic, emotional voice, conveying subtlety.": "节奏感强、情感丰富的声音，传达微妙之处。", "low, slow voice, evoking curiosity.": "低沉缓慢的声音，引发好奇。", "strong, passionate voice, driving action.": "强烈而充满激情的声音，推动行动。", "high, interrupted voice, expressing astonishment.": "高亢而断续的声音，表达惊讶。", "firm, powerful voice, persuasive and assuring.": "坚定有力的声音，说服力强且令人安心。", "sweet, gentle voice, suitable for emotional content.": "甜美温柔的声音，适合情感内容。", "shaky, interrupted voice, conveying anxiety.": "颤抖而断断续续的声音，传达出焦虑。", "deep, strong voice with emphasis, creating suspense.": "深沉有力的声音，带有强调，营造悬念。", "engaging, lively voice, emphasizing product benefits.": "吸引人且生动的语调，强调产品的优点。", "formal, clear voice with focus on key points.": "正式、清晰的语调，重点突出。", "calm, profound voice, delivering authenticity.": "冷静深沉的声音，传递真实感。", "standard, neutral voice, clear and precise.": "标准、中性、清晰准确的声音。", "bright, neutral voice, suitable for concise updates.": "明亮、中性的声音，适合简洁的更新。", "fast, lively voice, stimulating excitement.": "快速而充满活力的声音，激发兴奋。", "friendly, approachable voice, encouraging engagement.": "友好、平易近人的声音，鼓励互动。", "empathetic, gentle voice, easy to connect with.": "富有同情心、温柔的声音，容易与人产生共鸣。", "clear voice, emphasizing questions and answers.": "清晰的声音，强调问题和答案。", "cheerful, playful voice with a hint of mischief.": "欢快、顽皮的声音中带有一丝顽皮。", "slow, soft voice lacking energy.": "缓慢而柔和的声音缺乏能量。", "ironic, sharp voice, sometimes humorous.": "讽刺而尖锐的声音，有时又带有幽默感。", "cold voice, clearly expressing discomfort.": "冷冷的声音，清晰地表达出不适。", "soft, mysterious voice, creating intimacy.": "柔和而神秘的声音，营造出亲密感。", "emotional voice, convincing the listener to act.": "情感声音，促使听者采取行动。", "gentle voice, evoking feelings of reminiscence.": "温柔的声音，唤起回忆的感觉。", "even, relaxing voice, suitable for mindfulness.": "声音平稳放松，适合正念。", "clear voice, emphasizing key words.": "清晰的语音，强调关键词。", "confident, clear voice, ideal for business presentations.": "自信、清晰的声音，适合商务演示。", "natural, friendly voice, as if talking to a friend.": "自然、友好的声音，就像在和朋友聊天。", "fast, powerful voice, creating tension and excitement.": "快速而有力的声音，营造紧张和兴奋的氛围。", "emphasized, suspenseful voice, creating intensity.": "强调的、悬疑的声音，营造出强烈的紧张感。", "professional, formal voice, suitable for business content.": "专业、正式的语气，适合商业内容。", "energetic, lively voice, introducing new technologies.": "充满活力的声音，介绍新技术。", "vibrant, cheerful voice, appealing to younger audiences.": "充满活力、欢快的声音，吸引年轻观众。", "gentle, empathetic voice, easing concerns.": "温柔而富有同理心的声音，缓解担忧。", "strong, decisive voice, full of inspiration.": "有力而果断的声音，充满了启发。", "bright, excited voice, suitable for celebrations.": "明亮而兴奋的声音，适合庆祝活动。", "fast, strong voice, emphasizing urgency.": "快速、有力的声音，强调紧迫性。", "passionate, inspiring voice, encouraging action.": "充满激情，鼓舞人心的声音，激励行动。", "warm, approachable voice, fostering connection.": "温暖、平易近人的声音，促进联系。", "fast, powerful voice, brimming with enthusiasm.": "快速而强有力的声音，充满热情。", "slow, gentle voice, evoking peace and tranquility.": "缓慢而温柔的声音，唤起和平与宁静。", "firm, assertive voice, exuding confidence.": "坚定自信的声音，散发自信。", "warm, captivating voice, leaving a strong impression.": "温暖而迷人的声音，留下深刻的印象。", "flat, unvaried voice, conveying neutrality or irony.": "平淡无变的声音，传达中立或讽刺。", "curious voice, emphasizing questions.": "好奇的声音，强调问题。", "firm, clear voice, guiding the listener step-by-step.": "坚定而清晰的声音，引导听众一步一步地。", "gentle, slow voice, evoking a floating sensation.": "温柔缓慢的声音，唤起漂浮的感觉。", "deep, resonant voice, emphasizing grandeur.": "低沉而洪亮的声音，强调宏伟。", "soft, melodic voice, similar to singing.": "柔和的旋律声，类似于唱歌。", "low, drawn-out voice, evoking mystery.": "低沉而拖长的声音，引发神秘感。", "slow, low voice, conveying deep sadness.": "低沉的缓慢声音，传达出深深的悲伤。", "bright, energetic voice, full of positivity.": "明亮而充满活力的声音，充满积极性。", "low, whispery voice, evoking fear or strangeness.": "低沉而细语的声音，引起恐惧或怪异感。", "sweet, teasing voice, full of allure.": "甜美而充满诱惑的声音。", "slow, reflective voice, full of contemplation.": "缓慢而富有思考的声音，充满深思。", "resonant, emphasized voice, creating a movie-like effect.": "共鸣的、突出的声音，营造出电影般的效果。", "lighthearted, cheerful voice, sometimes exaggerated.": "轻松愉快，充满活力的声音，有时略显夸张。", "clear, slow voice, guiding the listener step-by-step.": "清晰缓慢的声音，引导听众一步一步地进行。", "natural voice, as if chatting with the listener.": "自然的语音，就像在与听者聊天一样。", "soft, sincere voice, expressing regret.": "轻柔真诚的声音，表达歉意。", "hesitant, uncertain voice, sometimes awkward.": "犹豫不决，不确定的声音，有时显得尴尬。", "warm voice, providing motivation and support.": "温暖的声音，提供动力和支持。", "even voice, free of emotional bias.": "即使是声音，也没有情绪偏见。", "strong, powerful voice, exuding credibility.": "强大有力的声音，充满可信度。", "cheerful voice with an undertone of mockery.": "愉快的声音中含有一丝嘲讽。", "gentle, empathetic voice, providing comfort.": "温柔且富有同情心的声音，带来安慰。", "clear, polite voice, suited for formal occasions.": "清晰礼貌的声音，适合正式场合。", "urgent, shaky voice, expressing distress.": "紧急，颤抖的声音，表露出痛苦。", "interrupted voice, mixed with light laughter.": "断断续续的声音，夹杂着轻轻的笑声。", "loud, emphasized voice, often humorous.": "响亮、强调的声音，常带有幽默感。", "flat, unemotional voice, conveying detachment.": "平淡而无感情的声音，传达出一种冷漠。", "fast, sharp voice, sometimes out of control.": "快速尖锐的声音，有时难以控制。", "warm, sincere voice, expressing appreciation.": "温暖而真诚的声音，表达谢意。", "low, subdued voice, full of remorse.": "低沉压抑的声音，充满悔恨。", "challenging, strong voice, full of insinuation.": "充满挑战，声音洪亮，暗示意味十足。", "loud, powerful voice, full of victory.": "响亮而有力的声音，充满胜利。", "low, cold voice, expressing determination for revenge.": "低沉而冰冷的声音，表达了复仇的决心。", "strong, inspiring voice, emphasizing heroic deeds.": "强烈而鼓舞人心的声音，强调英雄事迹。", "low, drawn-out voice, full of scheming.": "低沉而拖长的声音，充满了算计。", "even, repetitive voice, drawing the listener in.": "即使是重复的声音，也能吸引听众的注意力。", "urgent, shaky voice, expressing hopelessness.": "紧急、颤抖的声音，表达绝望。", "low, sorrowful voice, as if mourning.": "低沉而忧伤的声音，仿佛在哀悼。", "excited, joyful voice, full of festive spirit.": "激动、欢乐的声音，充满节日的气息。", "light, playful voice, sometimes mockingly.": "轻盈而戏谑的声音，有时带有嘲讽。", "weak, broken voice, expressing extreme fatigue.": "虚弱、沙哑的声音，表达出极度的疲惫。", "slow, emphasized voice, full of suspicion.": "缓慢而强调的语气，充满怀疑。", "bright, hopeful voice, creating positivity.": "明亮而充满希望的声音，带来积极向上的力量。", "Your audio will be processed with the latest stable model.": "您的音频将通过最新的稳定模型进行处理。", "Your audio will be processed with the latest beta model.": "您的音频将使用最新的测试模型进行处理。", "You don't have any saved prompts yet.": "您还没有保存任何提示。", "Commercial Use": "商业用途", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.": "您有权将我们服务生成的语音输出用于个人、教育或商业目的。但是，未经Text To Speech OpenAI事先书面同意，您不得转售、重新分发或再授权语音输出。", "Other people’s privacy": "他人的隐私", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "在使用我们的服务时，您必须尊重他人的隐私。未经许可，请勿上传或创建包含个人信息、机密数据或受版权保护的材料的语音输出。", "{price}$ per credit": "每学分{price}美元", "Pricing": "定价", "Simple and flexible. Only pay for what you use.": "简单灵活。只为所用付费。", "Pay as you go": "随用随付", "Flexible": "灵活", "Input characters": "输入字符", "Audio model": "音频模型", "Credits": "鸣谢", "Cost": "成本", "HD quality voices": "高清音质语音", "Advanced model": "高级模型", "Buy now": "立即购买", "Paste your text to calculate": "粘贴您的文本进行计算", "Paste your text here...": "将您的文本粘贴到此处...", "Calculate": "计算", "Estimate your cost by drag the slider below or": "通过拖动下面的滑块来估算您的费用或", "calming": "平静", "customer": "客户", "exciting": "令人兴奋", "excuse": "借口", "game": "游戏", "hot": "热", "kids": "孩子们", "professional": "专业", "tech": "科技", "trailer": "预告片", "weather": "天气", "No thumbnail available": "没有可用的缩略图", "Debit or Credit Card": "借记卡或信用卡", "Visa, Mastercard, American Express and more": "维萨卡、万事达卡、美国运通等", "Top up now": "立即充值", "noVideoAvailable": "没有可用视频"}