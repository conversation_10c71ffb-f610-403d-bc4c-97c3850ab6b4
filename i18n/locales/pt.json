{"system": "Gerador de Imagens IA", "helloWorld": "O<PERSON><PERSON>!", "Describe the image you want to generate...": "Descreva a imagem que você quer gerar...", "appTitle": "Criação de Imagens IA", "copyright": "Copyright © {year}, Criação de Imagens IA", "available": "Disponível para novos projetos", "notAvailable": "Não disponível no momento", "blog": "Blog", "copyLink": "Copiar link", "minRead": "MIN LEITURA", "articleLinkCopied": "Link do artigo copiado para a área de transferência", "clickToClose": "Clique em qualquer lugar ou pressione ESC para fechar", "promptDetails": "Detalhes do prompt", "generateWithPrompt": "Gerar com este prompt", "generateWithSettings": "Gerar com estas configurações", "preset": "Predefinição", "style": "<PERSON><PERSON><PERSON>", "resolution": "Resolução", "addImage": "<PERSON><PERSON><PERSON><PERSON>", "modelPreset": "Modelo/Predefinição", "imageDimensions": "<PERSON><PERSON><PERSON><PERSON><PERSON> da <PERSON>", "yourImage": "<PERSON><PERSON>", "generate": "<PERSON><PERSON><PERSON>", "nav": {"aitool": "Ferramenta IA", "api": "API", "login": "Entrar", "history": "História", "orders": "Pedidos"}, "3D Render": "Renderização 3D", "Acrylic": "Acrílico", "Anime General": "<PERSON><PERSON>", "Creative": "Criativo", "Dynamic": "Dinâmico", "Fashion": "Moda", "Game Concept": "Conceito de Jogo", "Graphic Design 3D": "Design Gráfico 3D", "Illustration": "Ilustração", "None": "<PERSON><PERSON><PERSON>", "Portrait": "Retrato", "Portrait Cinematic": "Retrato Cinematográfico", "Portrait Fashion": "Retrato de Moda", "Ray Traced": "Rastreamento de Raios", "Stock Photo": "Foto de Stock", "Watercolor": "Aquarela", "AI Image Generator": "Gerador de Imagens IA", "Generate AI images from text prompts with a magical particle transformation effect": "Gere imagens IA a partir de prompts de texto com um efeito mágico de transformação de partículas", "Enter your prompt": "Digite seu prompt", "Generating...": "Gerando...", "Generate Image": "<PERSON><PERSON><PERSON>", "Enter a prompt and click Generate Image to create an AI image": "Digite um prompt e clique em Gerar Imagem para criar uma imagem IA", "Prompt:": "Prompt:", "Download": "Baixar", "How It Works": "Como Funciona", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Este gerador de imagens IA usa um efeito de transformação baseado em partículas para visualizar o processo de criação. Quando você digita um prompt e clica em 'Gerar', o sistema:", "Sends your prompt to an AI image generation API": "Envia seu prompt para uma API de geração de imagens IA", "Creates a particle system with thousands of tiny particles": "Cria um sistema de partículas com milhares de pequenas partículas", "Transforms the random noise particles into the generated image": "Transforma as partículas de ruído aleatório na imagem gerada", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "As partículas começam em um padrão de ruído aleatório e então se transformam suavemente na imagem final, criando um efeito mágico que simula o processo criativo da IA.", "Transform": "Transformar", "Transforming...": "Transformando...", "Initializing particles...": "Inicializando partículas...", "Loading image...": "Carregando imagem...", "Creating particle system...": "Criando siste<PERSON> de partículas...", "Adding event listeners...": "Adicionando ouvintes de eventos...", "Ready!": "Pronto!", "AI Image Particle Effect": "Efeito de Partículas de Imagem IA", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "Uma demonstração do componente BaseMagicImage que transforma partículas em imagens geradas por IA", "Click anywhere or press ESC to close": "Clique em qualquer lugar ou pressione ESC para fechar", "auth": {"login": "Entrar", "loginDescription": "Entre na sua conta para continuar", "email": "Email", "enterEmail": "Digite seu email", "password": "<PERSON><PERSON>", "enterPassword": "Digite sua senha", "rememberMe": "<PERSON><PERSON><PERSON> de mim", "welcomeBack": "Bem-vindo de volta", "signupFailed": "Cadastro falhou", "signupFailedDescription": "Houve um erro durante o cadastro. Por favor, tente novamente.", "dontHaveAccount": "Não tem uma conta?", "signUp": "Cadastrar-se", "forgotPassword": "Esque<PERSON>u a senha?", "bySigningIn": "Ao entrar, você concorda com nossos", "termsOfService": "Termos de Serviço", "signUpTitle": "Cadastrar-se", "signUpDescription": "Crie uma conta para começar", "name": "Nome", "enterName": "Digite seu nome", "createAccount": "C<PERSON><PERSON> conta", "alreadyHaveAccount": "Já tem uma conta?", "bySigningUp": "Ao se cadastrar, você concorda com nossos", "backToHome": "Voltar ao início", "notVerifyAccount": "Sua conta não está verificada. Por favor, verifique sua conta para continuar", "verifyAccount": "Verificar conta", "resendActivationEmail": "Reenviar email de ativação", "accountRecovery": "Recuperação de Conta", "accountRecoveryTitle": "Recupere sua conta", "accountRecoveryDescription": "Digite seu email para receber instruções de redefinição de senha", "sendRecoveryEmail": "Enviar email de recuperação", "recoveryEmailSent": "Email de recuperação enviado", "recoveryEmailSentDescription": "Por favor, verifique seu email para instruções de redefinição de senha", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "resetPasswordTitle": "Redefina sua senha", "resetPasswordDescription": "Digite sua nova senha", "newPassword": "Nova senha", "confirmPassword": "Confirmar <PERSON><PERSON><PERSON>", "enterNewPassword": "Digite sua nova senha", "enterConfirmPassword": "Confirme sua nova senha", "passwordResetSuccess": "Redefinição de senha bem-sucedida", "passwordResetSuccessDescription": "Sua senha foi redefinida com sucesso. Agora você pode entrar com sua nova senha", "activateAccount": "Ativar Conta", "activateAccountTitle": "Ative sua conta", "activateAccountDescription": "Sua conta está sendo ativada...", "accountActivated": "Conta ativada", "accountActivatedDescription": "Sua conta foi ativada com sucesso. Agora você pode entrar", "activationFailed": "Ativação falhou", "activationFailedDescription": "Falha ao ativar sua conta. Por favor, tente novamente ou entre em contato com o suporte", "backToLogin": "Voltar ao login", "loginFailed": "<PERSON><PERSON> falhou", "loginWithGoogle": "Entrar com Google", "google": "Google", "filter": "Filter"}, "validation": {"invalidEmail": "<PERSON><PERSON>", "passwordMinLength": "A senha deve ter pelo menos 8 caracteres", "nameRequired": "Nome é obrigatório", "required": "Este campo é obrigatório", "passwordsDoNotMatch": "As senhas não coincidem"}, "imageSelect": {"pleaseSelectImageFile": "Por favor, selecione um arquivo de imagem", "selectedImage": "Imagem se<PERSON>", "removeImage": "Remover imagem"}, "pixelReveal": {"loading": "Carregando imagem...", "processing": "Processando imagem...", "revealComplete": "Revelação da imagem completa"}, "SIGNIN_WRONG_EMAIL_PASSWORD": "<PERSON>ail ou senha incorretos", "Try again": "Tentar novamente", "aiToolMenu": {"imagen": "Imagen", "videoGen": "Video Gen", "speechGen": "Speech Gen", "musicGen": "Music Gen", "imagen3": "Imagen 3", "imagen3Description": "Gere imagens de alta qualidade e detalhadas com renderização de texto precisa para conteúdo visual criativo.", "imagen4": "Imagen 4", "imagen4Description": "Expresse suas ideias como nunca antes — com Imagen, a criatividade não tem limites.", "gemini2Flash": "Gemini 2.0 Flash", "gemini2FlashDescription": "Gemini 2.0 Flash é uma ferramenta poderosa para gerar imagens a partir de prompts de texto.", "veo2": "Veo 2", "veo2Description": "<PERSON><PERSON> controle, consistência e criatividade do que nunca.", "veo3": "Veo 3", "veo3Description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Nosso mais recente modelo de geração de vídeo, projetado para capacitar cineastas e contadores de histórias.", "gemini25Pro": "Gemini 2.5 Pro", "gemini25ProDescription": "O modelo de texto para fala mais avançado disponível.", "gemini25Flash": "Gemini 2.5 Flash", "gemini25FlashDescription": "Processamento em larga escala (ex. múltiplos pdfs).\nTarefas de baixa latência e alto volume que requerem pensamento\nCasos de uso agênticos", "link": "Link", "linkDescription": "Use NuxtLink com superpoderes.", "soon": "Em breve"}, "readArticle": "Ler Artigo", "switchToLightMode": "Mudar para modo claro", "switchToDarkMode": "Mudar para modo escuro", "profile": "Perfil", "buyCredits": {"checkout": "Finalizar Compra", "checkoutDescription": "Confirme seu pedido e então escolha seu método de pagamento.", "orderDetail": "Detalhe do pedido", "credits": "C<PERSON>dit<PERSON>", "pricePerUnit": "Preço por unidade", "totalCredits": "Total de créditos", "totalPrice": "Preço total", "payment": "Pagamento", "submit": "Enviar", "cancel": "<PERSON><PERSON><PERSON>"}, "pricing": {"title": "Preços", "description": "Escolha o plano perfeito para suas necessidades de geração de imagens", "comingSoon": "Em Breve", "comingSoonDescription": "Nossos planos de preços estão sendo finalizados. Volte em breve para atualizações."}, "magicImageDemo": {"title": "Efeito de Partículas de Imagem IA", "description": "Uma demonstração do componente BaseMagicImage que transforma partículas em imagens geradas por IA", "image": "Imagem", "aboutTitle": "Sobre Este Componente", "aboutDescription": "O componente BaseMagicImage usa Three.js para criar um sistema de partículas que pode se transformar entre posições aleatórias e uma imagem gerada por IA. As partículas se movem com efeitos de redemoinho e fluxo, criando uma transformação mágica.", "featuresTitle": "Recursos", "features": {"particleRendering": "Renderização de imagem baseada em partículas", "smoothTransitions": "Transições suaves entre posições aleatórias de partículas e formação de imagem", "interactiveControls": "Controles de câmera interativos (arrastar para girar, rolar para zoom)", "customizable": "Contagem de partículas e duração de animação personalizáveis", "automatic": "Ativação de transformação automática ou manual"}, "howItWorksTitle": "Como Funciona", "howItWorksDescription": "O componente analisa os pixels de uma imagem e cria um sistema de partículas 3D onde cada partícula representa um pixel. Pixels mais brilhantes são posicionados mais próximos ao visualizador, criando um efeito 3D sutil. As partículas são inicialmente espalhadas aleatoriamente no espaço 3D, então se animam para formar a imagem quando ativadas."}, "privacy": {"title": "Política de Privacidade", "description": "Saiba como protegemos sua privacidade e lidamos com seus dados", "informationWeCollect": "Informações que Coletamos", "informationWeCollectDescription": "Coletamos informações que você nos fornece diretamente, como quando você cria uma conta, usa nossos serviços ou nos contata para suporte.", "howWeUseInformation": "Como Usamos Suas Informações", "howWeUseInformationDescription": "Usamos as informações que coletamos para fornecer, manter e melhorar nossos serviços, processar transações e nos comunicar com você.", "informationSharing": "Compartilhamento de Informações", "informationSharingDescription": "<PERSON>ão vendemos, trocamos ou transferimos suas informações pessoais para terceiros sem seu consentimento, exceto conforme descrito nesta política.", "dataSecurity": "Segurança de Dados", "dataSecurityDescription": "Implementamos medidas de segurança apropriadas para proteger suas informações pessoais contra acesso não autorizado, alteração, divulgação ou destruição.", "contactUs": "Entre em Contato", "contactUsDescription": "Se você tiver alguma dúvida sobre esta Política de Privacidade, entre em contato conosco através de nossos canais de suporte."}, "terms": {"title": "Termos de Serviço", "description": "Termos e condições para usar os serviços Imagen", "acceptanceOfTerms": "1. Aceitação dos Termos", "acceptanceOfTermsDescription": "Ao acessar e usar os serviços Imagen, você aceita e concorda em estar vinculado aos termos e disposições deste acordo.", "useOfService": "2. Uso do Serviço", "useOfServiceDescription": "Você concorda em usar nosso serviço apenas para fins legais e de acordo com estes Termos de Serviço.", "userAccounts": "3. <PERSON><PERSON> de Usuário", "userAccountsDescription": "Você é responsável por manter a confidencialidade de sua conta e senha.", "intellectualProperty": "4. <PERSON><PERSON><PERSON>ade Intelectual", "intellectualPropertyDescription": "Todo o conteúdo e materiais disponíveis em nosso serviço são protegidos por direitos de propriedade intelectual.", "termination": "5. <PERSON><PERSON><PERSON><PERSON>", "terminationDescription": "Podemos encerrar ou suspender sua conta e acesso ao serviço a nosso exclusivo critério.", "disclaimers": "6. Isenções de Responsabilidade", "disclaimersDescription": "O serviço é fornecido 'como está' sem garantias de qualquer tipo.", "contactUsTerms": "Entre em Contato", "contactUsTermsDescription": "Se você tiver alguma dúvida sobre estes Termos de Serviço, entre em contato conosco através de nossos canais de suporte."}, "Describe the video you want to generate...": "Descreva o vídeo que você deseja gerar...", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "imageStyles": {"selectImageStyle": "Selecionar Estilo de Imagem", "chooseStyle": "<PERSON><PERSON><PERSON><PERSON> estilo", "examples": "Exemplos", "selectStyleToSeeExamples": "Selecione um estilo para ver exemplos", "3d-render": {"description": "Criar imagens renderizadas em 3D fotorrealistas com iluminação e materiais adequados"}, "acrylic": {"description": "Estilo de pintura acrílica vibrante com pinceladas ousadas e cores ricas."}, "anime-general": {"description": "Estilo de anime e mangá japonês com personagens expressivos e cenários detalhados"}, "creative": {"description": "Estilo artístico e inovador com interpretações criativas e composições únicas."}, "dynamic": {"description": "Estilo de alta energia com movimento, ângulos dramáticos e composições poderosas"}, "fashion": {"description": "Estilo de fotografia de moda profissional com styling elegante e estética de luxo"}, "game-concept": {"description": "Estilo de arte conceitual de videogame com personagens e ambientes detalhados."}, "graphic-design-3d": {"description": "Design gráfico 3D moderno com linhas limpas e estética contemporânea"}, "illustration": {"description": "Estilo de ilustração tradicional com arte detalhada e técnicas artísticas"}, "none": {"description": "Sem um estilo específico aplicado, permitindo a interpretação natural da IA."}, "portrait": {"description": "Estilo de retrato profissional com foco nos traços faciais e expressões"}, "portrait-cinematic": {"description": "Estilo de retrato cinematográfico com iluminação dramática e qualidade semelhante a de filmes"}, "portrait-fashion": {"description": "Estilo de retrato de moda combinando elegância com técnicas profissionais de fotografia"}, "ray-traced": {"description": "Renderização avançada com traçado de raios com reflexos e iluminação realistas."}, "stock-photo": {"description": "Estilo de fotografia de banco de imagens comercial com aparência limpa e profissional."}, "watercolor": {"description": "Estilo de pintura em aquarela suave com cores fluidas e efeitos artísticos de pincel."}}, "appName": "GeminiGen.AI", "quickTopUp": "<PERSON><PERSON><PERSON>", "customTopUp": "Recarga personalizada", "numberOfCredits": "Número de créditos", "paypal": "PayPal", "paypalDescription": "Pague com segurança usando sua conta PayPal.", "debitCreditCard": "Cartão de Débito ou Crédito", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Pague com Cripto", "cryptoDescription": "Bitcoin, Ethereum e outras criptomoedas", "profileMenu": {"guide": "<PERSON><PERSON><PERSON>", "logo": "<PERSON><PERSON><PERSON>", "settings": "Configurações", "components": "Componentes"}, "loadingMoreItems": "Carregando mais itens...", "promptLabel": "Solicitação:", "videoExamples": "Exemplos de Vídeo", "videoExamplesDescription": "Explore estes exemplos de vídeo com seus prompts e configurações. Clique em qualquer botão 'Usar Este Prompt' para copiar o prompt para seu campo de entrada.", "useThisPrompt": "Use este prompt", "model": "<PERSON><PERSON>", "duration": "Duração", "videoTypeSelection": "Selecionar Tipo de Vídeo", "videoTypes": {"selectVideoType": "Selecionar Tipo de Vídeo", "searchPlaceholder": "Procurar tipos de vídeo...", "chooseType": "Esco<PERSON>her tipo", "examples": "Exemplos", "selectTypeToSeeExamples": "Selecione um tipo para ver exemplos.", "advertisingMarketing": {"title": "Publicidade e Marketing", "description": "Promova produtos/serviços, execute anúncios no Facebook, TikTok, YouTube, apresente marcas ou projetos, otimize os custos de produção de marketing em vídeo."}, "socialMediaContent": {"title": "Conteúdo de Mídias Sociais (Conteúdo de Curta Duração)", "description": "Vídeos curtos para TikTok, <PERSON><PERSON>, Shorts, vídeos de storytelling, resenhas, locuções com IA, converter textos ou blogs em vídeos."}, "personalEntertainment": {"title": "Conteúdo Criativo Pessoal / Entretenimento", "description": "Vídeos de comédia, paródias, memes, vídeos de contação de histórias, animação, conteúdo feito por fãs, curtas-metragens impulsionados por IA"}, "educationTraining": {"title": "Educação e Treinamento", "description": "Vídeos de ensino, e-learning, cursos, tutoriais de habilidades, compartilhamento de conhecimento, apresentações com instrutores avatar de IA"}, "corporateCommunication": {"title": "Comunicações Internas e Corporativo", "description": "Vídeos de integração, guias de processos, anúncios internos em vídeo, treinamento rápido de funcionários impulsionado por IA"}, "personalizedVideos": {"title": "Vídeos Personalizados", "description": "Desejos de aniversário, vídeos de aniversário, vídeos personalizados para clientes (email marketing, atendimento ao cliente), vídeos de recrutamento para candidatos específicos"}, "timeCostSavings": {"title": "Economia de Tempo e Custos na Produção", "description": "Não há necessidade de atores, estúdios, equipes de pós-produção, perfeito para indivíduos/equipes pequenas criando conteúdo rapidamente, fluxos de trabalho de produção de vídeo automatizados."}}, "notifications": {"title": "Notifications", "description": "Your recent notifications and updates", "totalCount": "{count} notifications", "markAllRead": "Mark all as read", "loadMore": "Load more", "close": "Close", "empty": {"title": "No notifications", "description": "You're all caught up! No new notifications to show."}, "error": {"title": "Error loading notifications"}, "types": {"default": {"title": "Notification", "description": "You have a new notification"}, "video_1": {"title": "Geração de Vídeo Pendente", "description": "A geração de vídeo está aguardando para ser processada."}, "video_2": {"title": "Geração de Vídeo Concluída", "description": "O vídeo foi gerado com sucesso."}, "video_3": {"title": "Geração de Vídeo Falhou", "description": "Falha na geração de vídeo"}, "image_1": {"title": "Geração de Imagem Pendente", "description": "A geração de imagem está aguardando para ser processada."}, "image_2": {"title": "Geração de Imagem Completa", "description": "A imagem foi gerada com sucesso."}, "image_3": {"title": "Falha na Geração da Imagem", "description": "A geração de imagem falhou"}, "tts_history_1": {"title": "Geração de Áudio Pendente", "description": "A conversão de texto em fala está aguardando para ser processada."}, "tts_history_2": {"title": "Geração de Áudio Concluída", "description": "Áudio de texto para fala foi gerado com sucesso."}, "tts_history_3": {"title": "Falha na Geração de Áudio", "description": "Falha na geração de texto para fala"}, "voice_training_1": {"title": "Treinamento de Voz Pendente", "description": "O treinamento de voz está aguardando para ser processado."}, "voice_training_2": {"title": "Treinamento de Voz Concluído", "description": "O treinamento do modelo de voz personalizado foi concluído com sucesso."}, "voice_training_3": {"title": "Treinamento de Voz Falhou", "description": "Treinamento de voz falhou"}, "music_1": {"title": "Geração de Música Pendente", "description": "A geração de música está aguardando para ser processada."}, "music_2": {"title": "Geração de Música Completa", "description": "Música de IA foi gerada com sucesso."}, "music_3": {"title": "Geração de Música Falhou", "description": "Geração de música falhou"}, "speech_1": {"title": "Geração de Discurso Pendente", "description": "Seu pedido de geração de discurso está aguardando para ser processado."}, "speech_2": {"title": "Geração de Discurso Completa", "description": "Seu discurso foi gerado com sucesso."}, "speech_3": {"title": "Geração de Discurso Falhou", "description": "A geração do seu discurso falhou. Por favor, tente novamente."}}, "time": {"justNow": "<PERSON><PERSON>a mesmo", "minutesAgo": "{minutes}m atrás", "hoursAgo": "{hours}h atrás", "yesterday": "Ontem"}, "status": {"processing": {"title": "Processamento", "description": "Seu pedido está sendo processado."}, "success": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Concluído com sucesso"}, "failed": {"title": "<PERSON><PERSON><PERSON>", "description": "Ocorreu um erro durante o processamento."}, "warning": {"title": "Aviso", "description": "Concluído com avisos"}, "pending": {"title": "Pendente", "description": "Aguardando para ser processado"}, "cancelled": {"title": "Cancelado", "description": "Solicitação foi cancelada"}}}, "footer": {"privacy": "Privacy", "terms": "Terms", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "textToSpeechOpenAI": "Text To Speech OpenAI", "doctransGPT": "DoctransGPT", "nuxtUIOnDiscord": "Nuxt UI on Discord", "youtube": "Youtube"}, "profileSettings": {"emailNotifications": "Email Notifications", "marketingEmails": "Marketing Emails", "securityAlerts": "Security Alerts"}, "settings": "Configurações", "userMenu": {"profile": "Perfil", "buyCredits": "<PERSON>mp<PERSON>", "settings": "Configurações", "api": "API", "logout": "<PERSON><PERSON>", "greeting": "<PERSON><PERSON><PERSON>, {name}"}, "formats": {"mp3": "MP3", "wav": "WAV"}, "channels": {"mono": "Mono", "stereo": "Estéreo"}, "options": {"allow": "<PERSON><PERSON><PERSON>", "dontAllow": "Não permitir", "voices": "Vozes", "pickVoice": "E<PERSON>lher voz"}, "voiceTypes": {"systemVoices": "Vozes do sistema", "customVoices": "<PERSON>ozes personaliza<PERSON>", "premiumVoices": "Vozes premium", "userVoices": "Vozes do usuário"}, "common": {"home": "Casa"}, "Describe the speech you want to generate...": "Descreva o discurso que você deseja gerar...", "listenToSpeech": "<PERSON><PERSON><PERSON> discurso", "generateSimilar": "<PERSON><PERSON><PERSON>", "voice": "Voz", "emotion": "Emoção", "speed": "Velocidade", "speed_settings": "Configurações de Velocidade", "speed_value": "Valor de Velocidade", "speed_slider": "Controle de Velocidade", "apply": "Aplicar", "speech_settings": "Configurações de Fala", "current_speed": "Velocidade Atual", "reset_defaults": "<PERSON><PERSON><PERSON>", "outputFormat": "Formato de Saída", "outputChannel": "Canal de Saída", "selectVoice": "Selecionar Voz", "selectEmotion": "Selecionar Emoção", "selectFormat": "Selecionar Formato", "selectChannel": "Selecionar Canal", "noVoicesAvailable": "Sem vozes disponíveis", "noEmotionsAvailable": "Sem emoções disponíveis", "searchVoices": "Procurar vozes...", "searchEmotions": "Buscar emoções...", "noVoicesFound": "Nenhuma voz encontrada", "noEmotionsFound": "Nenhuma emoção encontrada", "retry": "Tentar novamente", "noAudioSample": "Sem amostra de áudio disponível", "Speech Generation Complete": "Geração de fala concluída", "Your speech has been generated successfully": "Seu discurso foi gerado com sucesso.", "stripe": "Stripe", "stripeDescription": "Pague com segurança usando o Stripe", "history": {"tabs": {"imagen": "Imagem", "video": "Vídeo", "speech": "Discurso", "music": "Música", "history": "História"}}, "orders": {"title": "Histórico de Pedidos", "description": "Veja seu histórico de transações e pagamentos.", "orderId": "ID do Pedido", "amount": "Quantia", "credits": "C<PERSON>dit<PERSON>", "quantity": "Quantidade", "platform": "Plataforma", "externalId": "ID da Transação", "status": {"completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "success": "Sucesso", "paid": "Pago", "pending": "Pendente", "processing": "Processamento", "failed": "Fal<PERSON>", "cancelled": "Cancelado", "error": "Erro"}, "empty": {"title": "<PERSON>da sem pedidos.", "description": "Você ainda não fez nenhum pedido. Compre créditos para começar a usar nossos serviços.", "action": "<PERSON><PERSON><PERSON>"}, "endOfList": "Você viu todos os pedidos.", "errors": {"fetchFailed": "Falha ao carregar o histórico de pedidos. Por favor, tente novamente."}, "meta": {"title": "Histórico de Pedidos - Imagen AI", "description": "Visualize seu histórico de transações e pagamentos na Imagen AI"}}, "historyPages": {"imagenDescription": "Navegue pelas suas imagens e obras de arte geradas por IA.", "musicDescription": "Navegue pelo seu conteúdo musical e de áudio gerado por IA.", "speechDescription": "Navegue pelo seu conteúdo de fala e voz gerado por IA.", "videoDescription": "Navegue pelos seus vídeos e animações gerados por IA", "imagenBreadcrumb": "Imagem", "musicBreadcrumb": "Música", "speechBreadcrumb": "Discurso", "videoBreadcrumb": "Geração de Vídeo", "endOfImagesHistory": "Você chegou ao fim do histórico de imagens.", "endOfMusicHistory": "Você chegou ao fim da história da música.", "endOfSpeechHistory": "Você chegou ao fim do histórico de discursos.", "endOfVideoHistory": "Você chegou ao final do histórico de vídeos.", "noVideosFound": "Nenhum vídeo encontrado", "noVideosFoundDescription": "Comece a gerar vídeos para vê-los aqui.", "backToLibrary": "Voltar para a Biblioteca", "errorLoadingVideo": "Erro ao Carregar Vídeo", "loadingVideoDetails": "Carregando detalhes do vídeo...", "videoDetails": "Detalhes do Vídeo", "videoInformation": "Informações do Vídeo", "videoNotFound": "O vídeo que você está procurando não pôde ser encontrado ou carregado.", "aiContentLibraryTitle": "Biblioteca de Conteúdo de IA", "aiContentLibraryDescription": "Navegue e gerencie seu conteúdo gerado por IA em diferentes categorias."}, "demo": {"notifications": {"title": "Tipos de Notificação e Demonstração de Status", "description": "Exemplos de diferentes tipos de notificações com vários estados de status", "statusLegend": "Legenda de Status"}, "speechVoiceSelect": {"title": "Demonstração de Seleção de Voz de Fala", "description": "Demonstrando o componente reutilizável BaseSpeechVoiceSelectModal com a propriedade modelValue"}}, "models": {"imagen3": "Imagem 3", "gemini2Flash": "Gemini 2.0 Flash", "gemini25Flash": "Gemini 2.5 Flash", "sunoAI": "Suno AI", "udio": "Udio", "veo2": "Veo 2", "veo3": "Veo 3", "imagen4": "Imagem 4"}, "aspectRatio": "Proporção de Tela", "Image Reference": "Referência de Imagem", "personGeneration": {"dontAllow": "Don't Allow", "allowAdult": "Allow Adult", "allowAll": "Allow All"}, "safety_filter_level": "Nível de Filtro de Segurança", "used_credit": "<PERSON><PERSON><PERSON><PERSON>", "Safety Filter": "Filtro de Segurança", "safetyFilter": {"blockLowAndAbove": "Bloqueio Baixo e Acima", "blockMediumAndAbove": "Bloquear Médio e Superior", "blockOnlyHigh": "Bloquear Apenas Alto", "blockNone": "Bloquear Nenhum"}, "historyFilter": {"all": "Todos", "imagen": "Imagem", "videoGen": "Vídeo Gen", "speechGen": "Geração de Discurso"}, "Person Generation": "Geração de Pessoas", "downloadImage": "Baixar imagem", "noImageAvailable": "Imagem não disponível", "enhancePrompt": "Apr<PERSON><PERSON> Prompt", "addImages": "<PERSON><PERSON><PERSON><PERSON>", "generateVideo": "<PERSON><PERSON><PERSON>", "happy": "<PERSON><PERSON><PERSON>", "sad": "Triste", "angry": "<PERSON><PERSON>", "excited": "Animado", "laughing": "<PERSON><PERSON><PERSON>", "crying": "Chorando", "calm": "Calma", "serious": "<PERSON><PERSON><PERSON>", "frustrated": "<PERSON><PERSON><PERSON>", "hopeful": "Esperançoso", "narrative": "<PERSON><PERSON><PERSON><PERSON>", "kids' storytelling": "Histórias para Crianças", "audiobook": "Audiolivro", "poetic": "Poético", "mysterious": "Misterioso", "inspirational": "Inspirador", "surprised": "Surpreso", "confident": "<PERSON><PERSON><PERSON>", "romantic": "R<PERSON><PERSON><PERSON><PERSON>", "scared": "<PERSON><PERSON><PERSON><PERSON>", "trailer voice": "Voz do Trailer", "advertising": "Publicidade", "speech": "Discurso", "documentary": "Documentário", "newsreader": "<PERSON><PERSON>í<PERSON>", "weather report": "Relatório do Tempo", "game commentary": "Comentário do Jogo", "interactive": "Interativo", "customer support": "Apoio ao Cliente", "playful": "Divertido", "tired": "Cansado", "sarcastic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgusted": "<PERSON><PERSON><PERSON>", "whispering": "Sussurrando", "persuasive": "<PERSON><PERSON><PERSON><PERSON>", "nostalgic": "Nostálgico", "meditative": "Meditativo", "announcement": "<PERSON><PERSON><PERSON>", "professional pitch": "Apresentação Profissional", "casual": "Casual", "exciting trailer": "Trailer <PERSON>", "dramatic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "corporate": "Corporativo", "tech enthusiast": "Entusiasta da Tecnologia", "youthful": "Juvenil", "calming reassurance": "Tranquilizante Reafirmação", "heroic": "Heroico", "festive": "<PERSON><PERSON>", "urgent": "Urgente", "motivational": "Motivacional", "friendly": "Amigável", "energetic": "Energético", "serene": "<PERSON><PERSON>", "bold": "Negrito", "charming": "Encan<PERSON><PERSON>", "monotone": "Monótono", "questioning": "Questionamento", "directive": "Diretiva", "dreamy": "<PERSON><PERSON><PERSON>", "epic": "Épico", "lyrical": "<PERSON><PERSON><PERSON><PERSON>", "mystical": "Místico", "melancholy": "Melancolia", "cheerful": "Alegre", "eerie": "<PERSON><PERSON><PERSON><PERSON>", "flirtatious": "Fler<PERSON><PERSON>", "thoughtful": "Reflexivo", "cinematic": "Cinemático", "humorous": "Humorístico", "instructional": "Instrucional", "conversational": "Conversacional", "apologetic": "Apologé<PERSON><PERSON>", "excuse-making": "Elaboração de desculpas", "encouraging": "Encorajador", "neutral": "Neutro", "authoritative": "Autoritário", "sarcastic cheerful": "Sarcástico Alegre", "reassuring": "Reconfortante", "formal": "Formal", "anguished": "<PERSON><PERSON><PERSON>", "giggling": "<PERSON><PERSON><PERSON>", "exaggerated": "Exagerado", "cold": "<PERSON><PERSON>", "hot-tempered": "Temperamental", "grateful": "Grato", "regretful": "<PERSON><PERSON>pendi<PERSON>", "provocative": "Provocativo", "triumphant": "<PERSON><PERSON><PERSON><PERSON>", "vengeful": "Vingativo", "heroic narration": "Narração Heroica", "villainous": "Vilãnsco", "hypnotic": "Hipnótico", "desperate": "Desesperado", "lamenting": "<PERSON><PERSON><PERSON><PERSON>", "celebratory": "Comemorativo", "teasing": "Provocação", "exhausted": "<PERSON><PERSON><PERSON>", "questioning suspicious": "<PERSON><PERSON><PERSON>", "optimistic": "Otimista", "bright, gentle voice, expressing excitement.": "Voz clara e suave, expressando en<PERSON>.", "low, slow voice, conveying deep emotions.": "Voz baixa e lenta, transmitindo emoções profundas.", "sharp, exaggerated voice, expressing frustration.": "Voz aguda e exagerada, expressando frustração.", "fast, lively voice, full of enthusiasm.": "Voz rápida e animada, cheia de entusiasmo.", "interrupted, joyful voice, interspersed with laughter.": "Voz interrompida e alegre, entremeada de risadas.", "shaky, low voice, expressing pain.": "Voz trêmula e baixa, expressando dor.", "gentle, steady voice, providing reassurance.": "Voz suave e constante, proporcionando segu<PERSON>.", "mature, clear voice, suitable for formal content.": "Voz madura e clara, adequada para conteúdo formal.", "weary, slightly irritated voice.": "Voz cansada, ligeiramente irritada.", "bright voice, conveying positivity and hope.": "Voz radiante, transmitindo positividade e esperança.", "natural, gentle voice with a slow rhythm.": "Voz natural e suave com um ritmo lento.", "lively, engaging voice, captivating for children.": "Voz animada e envolvente, cativante para crianças.", "even, slow voice, emphasizing content meaning.": "<PERSON>oz calma, pausada, valorizando o significado do conteúdo.", "rhythmic, emotional voice, conveying subtlety.": "Voz rítmica e emocional, transmitindo sutileza.", "low, slow voice, evoking curiosity.": "Voz baixa e lenta, evocando curiosidade.", "strong, passionate voice, driving action.": "Voz forte e apaixonada, impulsionando a ação.", "high, interrupted voice, expressing astonishment.": "<PERSON>oz alta, interrompida, expressando espanto.", "firm, powerful voice, persuasive and assuring.": "Voz firme, poderosa, persuasiva e tranquilizadora.", "sweet, gentle voice, suitable for emotional content.": "Voz doce e suave, adequada para conteúdo emocional.", "shaky, interrupted voice, conveying anxiety.": "Voz trêmula e interrompida, transmitindo ansiedade.", "deep, strong voice with emphasis, creating suspense.": "Voz profunda e forte com ênfase, criando suspense.", "engaging, lively voice, emphasizing product benefits.": "Voz envolvente e animada, destacando os benefícios do produto.", "formal, clear voice with focus on key points.": "Voz formal e clara com foco nos pontos principais.", "calm, profound voice, delivering authenticity.": "Voz calma e profunda, transmitindo autenticidade.", "standard, neutral voice, clear and precise.": "<PERSON><PERSON> pad<PERSON>, neutra, clara e precisa.", "bright, neutral voice, suitable for concise updates.": "Voz clara e neutra, adequada para atualizações concisas.", "fast, lively voice, stimulating excitement.": "Voz rápida e animada, estimulando a excitação.", "friendly, approachable voice, encouraging engagement.": "Voz amigável e acessível, incentivando o engajamento.", "empathetic, gentle voice, easy to connect with.": "Voz empática, amável e de fácil conex<PERSON>.", "clear voice, emphasizing questions and answers.": "<PERSON>oz clara, enfatizando perguntas e respostas.", "cheerful, playful voice with a hint of mischief.": "Voz alegre e brincalhona com um toque de travessura.", "slow, soft voice lacking energy.": "Voz lenta e suave, sem energia.", "ironic, sharp voice, sometimes humorous.": "Voz irônica, afiada, por vezes humorística.", "cold voice, clearly expressing discomfort.": "<PERSON>oz fria, expressando claramente desconforto.", "soft, mysterious voice, creating intimacy.": "Voz suave e misteriosa, criando intimidade.", "emotional voice, convincing the listener to act.": "<PERSON>oz emocional, convencendo o ouvinte a agir.", "gentle voice, evoking feelings of reminiscence.": "<PERSON>oz suave, evocando sentimentos de reminiscência.", "even, relaxing voice, suitable for mindfulness.": "Voz uniforme e relaxante, adequada para a atenção plena.", "clear voice, emphasizing key words.": "<PERSON>oz clara, enfatizando palavras-chave.", "confident, clear voice, ideal for business presentations.": "Voz confiante e clara, ideal para apresentações empresariais.", "natural, friendly voice, as if talking to a friend.": "Voz natural e amigável, como se estivesse conversando com um amigo.", "fast, powerful voice, creating tension and excitement.": "Voz rápida e poderosa, criando tensão e empolgação.", "emphasized, suspenseful voice, creating intensity.": "Voz enfatizada e carregada de suspense, criando intensidade.", "professional, formal voice, suitable for business content.": "Voz profissional e formal, adequada para conteúdo empresarial.", "energetic, lively voice, introducing new technologies.": "Voz enérgica e animada, introduzindo novas tecnologias.", "vibrant, cheerful voice, appealing to younger audiences.": "Voz vibrante e alegre, atraente para públicos mais jovens.", "gentle, empathetic voice, easing concerns.": "Voz gentil e empática, acalmando preocupações.", "strong, decisive voice, full of inspiration.": "Voz forte e decisiva, cheia de inspiração.", "bright, excited voice, suitable for celebrations.": "Voz brilhante e animada, adequada para celebrações.", "fast, strong voice, emphasizing urgency.": "Voz rápida e forte, enfatizando urgência.", "passionate, inspiring voice, encouraging action.": "Voz apaixonada e inspiradora, incentivando a ação.", "warm, approachable voice, fostering connection.": "Voz calorosa e acessível, promovendo <PERSON>.", "fast, powerful voice, brimming with enthusiasm.": "Voz rápida e poderosa, repleta de entusiasmo.", "slow, gentle voice, evoking peace and tranquility.": "Voz lenta e suave, evocando paz e tranquilidade.", "firm, assertive voice, exuding confidence.": "Voz firme e assertiva, exalando confiança.", "warm, captivating voice, leaving a strong impression.": "Voz quente e cativante, deixando uma forte impressão.", "flat, unvaried voice, conveying neutrality or irony.": "Voz plana e invariante, transmitindo neutralidade ou ironia.", "curious voice, emphasizing questions.": "<PERSON>oz curiosa, enfatizando pergun<PERSON>.", "firm, clear voice, guiding the listener step-by-step.": "Voz firme e clara, guiando o ouvinte passo a passo.", "gentle, slow voice, evoking a floating sensation.": "Voz suave e lenta, evocando uma sensação de flutuar.", "deep, resonant voice, emphasizing grandeur.": "Voz profunda e ressonante, enfatizando a grandiosidade.", "soft, melodic voice, similar to singing.": "Voz suave e melódica, semelhante a canto.", "low, drawn-out voice, evoking mystery.": "Voz baixa e arrastada, evocando mistério.", "slow, low voice, conveying deep sadness.": "Voz lenta e baixa, transmitindo profunda tristeza.", "bright, energetic voice, full of positivity.": "Voz brilhante e energética, cheia de positividade.", "low, whispery voice, evoking fear or strangeness.": "Voz baixa e sussurrante, evocando medo ou estranheza.", "sweet, teasing voice, full of allure.": "<PERSON>oz doce, provocante, cheia de encanto.", "slow, reflective voice, full of contemplation.": "Voz lenta e reflexiva, cheia de contemplação.", "resonant, emphasized voice, creating a movie-like effect.": "Voz ressonante e enfatizada, criando um efeito cinematográfico.", "lighthearted, cheerful voice, sometimes exaggerated.": "Voz alegre e animada, às vezes exagerada.", "clear, slow voice, guiding the listener step-by-step.": "Voz clara e pausada, guiando o ouvinte passo a passo.", "natural voice, as if chatting with the listener.": "Voz natural, como se estivesse conversando com o ouvinte.", "soft, sincere voice, expressing regret.": "Voz suave e sincera, expressando arrependimento.", "hesitant, uncertain voice, sometimes awkward.": "<PERSON><PERSON> <PERSON><PERSON>, incerta, às vezes desajeitada.", "warm voice, providing motivation and support.": "Voz acolhedora, proporcionando motivação e apoio.", "even voice, free of emotional bias.": "Voz equilibrada, livre de viés emocional.", "strong, powerful voice, exuding credibility.": "Voz forte e poderosa, exalando credibilidade.", "cheerful voice with an undertone of mockery.": "Voz alegre com um tom de zombaria.", "gentle, empathetic voice, providing comfort.": "Voz suave e empática, oferecendo conforto.", "clear, polite voice, suited for formal occasions.": "Voz clara e educada, adequada para ocasiões formais.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON><PERSON>, voz tr<PERSON><PERSON><PERSON>, <PERSON>ando an<PERSON>.", "interrupted voice, mixed with light laughter.": "Voz interrompida, misturada com risadinhas.", "loud, emphasized voice, often humorous.": "Voz alta e enfatizada, muitas vezes humorística.", "flat, unemotional voice, conveying detachment.": "<PERSON>oz plana, sem emoção, transmitindo distanciamento.", "fast, sharp voice, sometimes out of control.": "<PERSON>oz r<PERSON>a, aguda, às vezes fora de controle.", "warm, sincere voice, expressing appreciation.": "Voz calorosa e sincera, expressando gratid<PERSON>.", "low, subdued voice, full of remorse.": "Voz baixa e contida, cheia de remorso.", "challenging, strong voice, full of insinuation.": "<PERSON><PERSON><PERSON><PERSON>, voz forte, cheia de insinuação.", "loud, powerful voice, full of victory.": "Voz alta e poderosa, cheia de vitória.", "low, cold voice, expressing determination for revenge.": "Voz baixa e fria, expressando determinação por vingança.", "strong, inspiring voice, emphasizing heroic deeds.": "Voz forte e inspiradora, enfatizando feitos heróicos.", "low, drawn-out voice, full of scheming.": "Voz baixa e prolongada, cheia de tramas.", "even, repetitive voice, drawing the listener in.": "Voz uniforme e repetitiva, cativando o ouvinte.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON><PERSON>, voz trê<PERSON>la, expressando desespero.", "low, sorrowful voice, as if mourning.": "<PERSON>oz baixa e triste, como se estivesse de luto.", "excited, joyful voice, full of festive spirit.": "<PERSON><PERSON> em<PERSON>ga<PERSON>, alegre, cheia de espírito festivo.", "light, playful voice, sometimes mockingly.": "Voz leve e brincalhona, às vezes zombeteira.", "weak, broken voice, expressing extreme fatigue.": "Voz fraca e quebrada, expressando fadiga extrema.", "slow, emphasized voice, full of suspicion.": "<PERSON><PERSON> lenta, en<PERSON><PERSON><PERSON><PERSON>, cheia de suspeita.", "bright, hopeful voice, creating positivity.": "Voz radiante e esperançosa, criando positividade.", "Your audio will be processed with the latest stable model.": "Seu áudio será processado com o modelo estável mais recente.", "Your audio will be processed with the latest beta model.": "Seu áudio será processado com o modelo beta mais recente.", "You don't have any saved prompts yet.": "Você ainda não tem prompts salvos.", "Commercial Use": "<PERSON><PERSON>l", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.": "Você tem o direito de usar a saída de voz gerada por nossos serviços para fins pessoais, educacionais ou comerciais. No entanto, você não pode revender, redistribuir ou sublicenciar a saída de voz sem consentimento prévio por escrito da Text To Speech OpenAI.", "Other people’s privacy": "A privacidade de outras pessoas", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "Você deve respeitar a privacidade dos outros ao usar nossos serviços. Não carregue ou crie saídas de fala que contenham informações pessoais, dados confidenciais ou material protegido por direitos autorais sem permissão.", "{price}$ per credit": "{price}$ por crédito", "Pricing": "Preços", "Simple and flexible. Only pay for what you use.": "Simples e flexível. Pague apenas pelo que usar.", "Pay as you go": "Pague conforme o uso", "Flexible": "Flexível", "Input characters": "Caracteres de entrada", "Audio model": "<PERSON><PERSON>", "Credits": "C<PERSON>dit<PERSON>", "Cost": "Custo", "HD quality voices": "Vozes de qualidade HD", "Advanced model": "<PERSON><PERSON>", "Buy now": "Compre agora", "Paste your text to calculate": "Cole seu texto para calcular", "Paste your text here...": "Cole seu texto aqui...", "Calculate": "Calcular", "Estimate your cost by drag the slider below or": "Estime seu custo arrastando o controle deslizante abaixo ou", "calming": "Calmante", "customer": "Cliente", "exciting": "empol<PERSON>e", "excuse": "<PERSON><PERSON><PERSON><PERSON>", "game": "Jogo", "hot": "<PERSON><PERSON>", "kids": "Crian<PERSON><PERSON>", "professional": "Profissional", "tech": "Tecnologia", "trailer": "Reboque", "weather": "Tempo", "No thumbnail available": "Nenhuma miniatura disponível", "Debit or Credit Card": "Cartão de Débito ou Crédito", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express e mais", "Top up now": "Recar<PERSON><PERSON> agora", "noVideoAvailable": "Sem vídeo disponível"}