{"common": {"home": "Home", "back": "Back", "edit": "Edit", "save": "Save", "cancel": "Cancel", "delete": "Delete", "copy": "Copy", "copied": "<PERSON>pied", "manage": "Manage"}, "system": "AI Image Generator", "helloWorld": "Hello World!", "Describe the image you want to generate...": "Describe the image you want to generate...", "Describe the video you want to generate...": "Describe the video you want to generate...", "Describe the speech you want to generate...": "Describe the speech you want to generate...", "appTitle": "GeminiGen.AI", "copyright": "Copyright © {year}, GeminiGen.AI", "available": "Available for new projects", "notAvailable": "Not available at the moment", "blog": "Blog", "copyLink": "Copy link", "minRead": "MIN READ", "articleLinkCopied": "Article link copied to clipboard", "clickToClose": "Click anywhere or press ESC to close", "promptDetails": "Prompt details", "generateWithPrompt": "Generate with this prompt", "generateWithSettings": "Generate with these settings", "preset": "Preset", "style": "Style", "resolution": "Resolution", "addImage": "Add Image", "modelPreset": "Model/Preset", "imageDimensions": "Image Dimensions", "yourImage": "Your Image", "generate": "Generate", "cancel": "Cancel", "confirm": "Confirm", "listenToSpeech": "Listen to Speech", "generateSimilar": "Generate Similar", "Safety Filter": "Safety Filter", "personGeneration": {"dontAllow": "Don't Allow", "allowAdult": "Allow Adult", "allowAll": "Allow All"}, "safetyFilter": {"blockLowAndAbove": "Block Low and Above", "blockMediumAndAbove": "Block Medium and Above", "blockOnlyHigh": "Block Only High", "blockNone": "Block None"}, "nav": {"aitool": "AI Tool", "history": "History", "orders": "Orders", "api": "API", "login": "<PERSON><PERSON>"}, "3D Render": "3D Render", "Acrylic": "Acrylic", "Anime General": "Anime General", "Creative": "Creative", "Dynamic": "Dynamic", "Fashion": "Fashion", "Game Concept": "Game Concept", "Graphic Design 3D": "Graphic Design 3D", "Illustration": "Illustration", "None": "None", "Portrait": "Portrait", "Portrait Cinematic": "Portrait Cinematic", "Portrait Fashion": "Portrait Fashion", "Ray Traced": "<PERSON>", "Stock Photo": "Stock Photo", "Watercolor": "Watercolor", "voice": "Voice", "emotion": "Emotion", "speed": "Speed", "speed_settings": "Speed Settings", "speed_value": "Speed Value", "speed_slider": "Speed Slider", "apply": "Apply", "speech_settings": "Speech Settings", "current_speed": "Current Speed", "reset_defaults": "Reset to Defaults", "outputFormat": "Output Format", "outputChannel": "Output Channel", "selectVoice": "Select Voice", "selectEmotion": "Select Emotion", "selectFormat": "Select Format", "selectChannel": "Select Channel", "noVoicesAvailable": "No voices available", "noEmotionsAvailable": "No emotions available", "searchVoices": "Search voices...", "searchEmotions": "Search emotions...", "noVoicesFound": "No voices found", "noEmotionsFound": "No emotions found", "retry": "Retry", "noAudioSample": "No audio sample available", "Speech Generation Complete": "Speech Generation Complete", "Your speech has been generated successfully": "Your speech has been generated successfully", "AI Image Generator": "AI Image Generator", "Generate AI images from text prompts with a magical particle transformation effect": "Generate AI images from text prompts with a magical particle transformation effect", "Enter your prompt": "Enter your prompt", "Generating...": "Generating...", "Generate Image": "Generate Image", "Enter a prompt and click Generate Image to create an AI image": "Enter a prompt and click Generate Image to create an AI image", "Prompt:": "Prompt:", "Download": "Download", "How It Works": "How It Works", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:", "Sends your prompt to an AI image generation API": "Sends your prompt to an AI image generation API", "Creates a particle system with thousands of tiny particles": "Creates a particle system with thousands of tiny particles", "Transforms the random noise particles into the generated image": "Transforms the random noise particles into the generated image", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.", "Transform": "Transform", "Transforming...": "Transforming...", "Initializing particles...": "Initializing particles...", "Loading image...": "Loading image...", "Creating particle system...": "Creating particle system...", "Adding event listeners...": "Adding event listeners...", "Ready!": "Ready!", "AI Image Particle Effect": "AI Image Particle Effect", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images", "Click anywhere or press ESC to close": "Click anywhere or press ESC to close", "auth": {"login": "<PERSON><PERSON>", "loginDescription": "Login to your account to continue", "email": "Email", "enterEmail": "Enter your email", "filter": "Filter", "password": "Password", "enterPassword": "Enter your password", "rememberMe": "Remember me", "welcomeBack": "Welcome back", "signupFailed": "Signup failed", "signupFailedDescription": "There was an error during signup. Please try again.", "dontHaveAccount": "Don't have an account?", "signUp": "Sign up", "forgotPassword": "Forgot password?", "bySigningIn": "By signing in, you agree to our", "termsOfService": "Terms of Service", "signUpTitle": "Sign up", "signUpDescription": "Create an account to get started", "name": "Name", "enterName": "Enter your name", "createAccount": "Create account", "alreadyHaveAccount": "Already have an account?", "bySigningUp": "By signing up, you agree to our", "backToHome": "Back to home", "notVerifyAccount": "Your account is not verified. Please verify your account to continue", "verifyAccount": "Verify account", "resendActivationEmail": "Resend activation email", "accountRecovery": "Account Recovery", "accountRecoveryTitle": "Recover your account", "accountRecoveryDescription": "Enter your email to receive password reset instructions", "sendRecoveryEmail": "Send recovery email", "recoveryEmailSent": "Recovery email sent", "recoveryEmailSentDescription": "Please check your email for password reset instructions", "resetPassword": "Reset Password", "resetPasswordTitle": "Reset your password", "resetPasswordDescription": "Enter your new password", "newPassword": "New password", "confirmPassword": "Confirm password", "enterNewPassword": "Enter your new password", "enterConfirmPassword": "Confirm your new password", "passwordResetSuccess": "Password reset successful", "passwordResetSuccessDescription": "Your password has been reset successfully. You can now login with your new password", "activateAccount": "Activate Account", "activateAccountTitle": "Activate your account", "activateAccountDescription": "Your account is being activated...", "accountActivated": "Account activated", "accountActivatedDescription": "Your account has been activated successfully. You can now login", "activationFailed": "Activation failed", "activationFailedDescription": "Failed to activate your account. Please try again or contact support", "backToLogin": "Back to login", "loginFailed": "<PERSON><PERSON> failed", "loginWithGoogle": "Login with Google", "google": "Google"}, "validation": {"invalidEmail": "Invalid email", "passwordMinLength": "Password must be at least 8 characters", "nameRequired": "Name is required", "required": "This field is required", "passwordsDoNotMatch": "Passwords do not match"}, "imageSelect": {"pleaseSelectImageFile": "Please select an image file", "selectedImage": "Selected image", "removeImage": "Remove image"}, "imageStyles": {"selectImageStyle": "Select Image Style", "chooseStyle": "Choose Style", "examples": "Examples", "selectStyleToSeeExamples": "Select a style to see examples", "3d-render": {"description": "Create photorealistic 3D rendered images with proper lighting and materials"}, "acrylic": {"description": "Vibrant acrylic painting style with bold brushstrokes and rich colors"}, "anime-general": {"description": "Japanese anime and manga style with expressive characters and detailed backgrounds"}, "creative": {"description": "Artistic and innovative style with creative interpretations and unique compositions"}, "dynamic": {"description": "High-energy style with motion, dramatic angles, and powerful compositions"}, "fashion": {"description": "Professional fashion photography style with elegant styling and luxury aesthetics"}, "game-concept": {"description": "Video game concept art style with detailed characters and environments"}, "graphic-design-3d": {"description": "Modern 3D graphic design with clean lines and contemporary aesthetics"}, "illustration": {"description": "Traditional illustration style with detailed artwork and artistic techniques"}, "none": {"description": "No specific style applied, allowing natural AI interpretation"}, "portrait": {"description": "Professional portrait style with focus on facial features and expressions"}, "portrait-cinematic": {"description": "Cinematic portrait style with dramatic lighting and movie-like quality"}, "portrait-fashion": {"description": "Fashion portrait style combining elegance with professional photography techniques"}, "ray-traced": {"description": "Advanced ray-traced rendering with realistic reflections and lighting"}, "stock-photo": {"description": "Commercial stock photography style with clean, professional appearance"}, "watercolor": {"description": "Soft watercolor painting style with flowing colors and artistic brush effects"}}, "pixelReveal": {"loading": "Loading image...", "processing": "Processing image...", "revealComplete": "Image reveal complete"}, "SIGNIN_WRONG_EMAIL_PASSWORD": "Wrong email or password", "Try again": "Try again", "aiToolMenu": {"imagen": "Imagen", "videoGen": "Video Gen", "speechGen": "Speech Gen", "musicGen": "Music Gen", "imagen3": "Imagen 3", "imagen3Description": "Generate high-quality, detailed images with accurate text rendering for creative visual content.", "imagen4": "Imagen 4", "imagen4Description": "Express your ideas like never before — with <PERSON><PERSON>, creativity has no limits.", "gemini2Flash": "Gemini 2.0 Flash", "gemini2FlashDescription": "Gemini 2.0 Flash is a powerful tool for generating images from text prompts.", "veo2": "Veo 2", "veo2Description": "Greater control, consistency, and creativity than ever before.", "veo3": "Veo 3", "veo3Description": "Video, meet audio. Our latest video generation model, designed to empower filmmakers and storytellers.", "gemini25Pro": "Gemini 2.5 Pro", "gemini25ProDescription": "The most advanced text-to-speech model available.", "gemini25Flash": "Gemini 2.5 Flash", "gemini25FlashDescription": "Large scale processing (e.g. multiple pdfs).\nLow latency, high volume tasks which require thinking\nAgentic use cases", "link": "Link", "linkDescription": "Use NuxtLink with superpowers.", "soon": "Soon"}, "readArticle": "Read Article", "switchToLightMode": "Switch to light mode", "switchToDarkMode": "Switch to dark mode", "profile": "Profile", "profileSettings": {"emailNotifications": "Email Notifications", "marketingEmails": "Marketing Emails", "securityAlerts": "Security Alerts"}, "buyCredits": {"checkout": "Checkout", "checkoutDescription": "Confirm your order then choose your payment method.", "orderDetail": "Order detail", "credits": "Credits", "pricePerUnit": "Price per unit", "totalCredits": "Total credits", "totalPrice": "Total price", "payment": "Payment", "submit": "Submit", "cancel": "Cancel"}, "pricing": {"title": "Pricing", "description": "Choose the perfect plan for your image generation needs", "comingSoon": "Coming Soon", "comingSoonDescription": "Our pricing plans are being finalized. Check back soon for updates."}, "magicImageDemo": {"title": "AI Image Particle Effect", "description": "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images", "image": "Image", "aboutTitle": "About This Component", "aboutDescription": "The BaseMagicImage component uses Three.js to create a particle system that can transform between random positions and an AI-generated image. The particles move with swirling and flowing effects, creating a magical transformation.", "featuresTitle": "Features", "features": {"particleRendering": "Particle-based image rendering", "smoothTransitions": "Smooth transitions between random particle positions and image formation", "interactiveControls": "Interactive camera controls (drag to rotate, scroll to zoom)", "customizable": "Customizable particle count and animation duration", "automatic": "Automatic or manual transformation triggering"}, "howItWorksTitle": "How It Works", "howItWorksDescription": "The component analyzes the pixels of an image and creates a 3D particle system where each particle represents a pixel. Brighter pixels are positioned closer to the viewer, creating a subtle 3D effect. The particles are initially scattered randomly in 3D space, then animate to form the image when triggered."}, "privacy": {"title": "Privacy Policy", "description": "Learn how we protect your privacy and handle your data", "informationWeCollect": "Information We Collect", "informationWeCollectDescription": "We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support.", "howWeUseInformation": "How We Use Your Information", "howWeUseInformationDescription": "We use the information we collect to provide, maintain, and improve our services, process transactions, and communicate with you.", "informationSharing": "Information Sharing", "informationSharingDescription": "We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy.", "dataSecurity": "Data Security", "dataSecurityDescription": "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.", "contactUs": "Contact Us", "contactUsDescription": "If you have any questions about this Privacy Policy, please contact us through our support channels."}, "terms": {"title": "Terms of Service", "description": "Terms and conditions for using Imagen services", "acceptanceOfTerms": "1. Acceptance of Terms", "acceptanceOfTermsDescription": "By accessing and using Imagen services, you accept and agree to be bound by the terms and provision of this agreement.", "useOfService": "2. Use of Service", "useOfServiceDescription": "You agree to use our service only for lawful purposes and in accordance with these Terms of Service.", "userAccounts": "3. User Accounts", "userAccountsDescription": "You are responsible for maintaining the confidentiality of your account and password.", "intellectualProperty": "4. Intellectual Property", "intellectualPropertyDescription": "All content and materials available on our service are protected by intellectual property rights.", "termination": "5. Termination", "terminationDescription": "We may terminate or suspend your account and access to the service at our sole discretion.", "disclaimers": "6. <PERSON><PERSON><PERSON>", "disclaimersDescription": "The service is provided 'as is' without any warranties of any kind.", "contactUsTerms": "Contact Us", "contactUsTermsDescription": "If you have any questions about these Terms of Service, please contact us through our support channels."}, "appName": "GeminiGen.AI", "quickTopUp": "Quick top up", "customTopUp": "Custom top up", "numberOfCredits": "Number of credits", "paypal": "PayPal", "paypalDescription": "Pay securely with your PayPal account", "stripe": "Stripe", "stripeDescription": "Pay securely with <PERSON>e", "debitCreditCard": "Debit or Credit Card", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Pay with Crypto", "cryptoDescription": "Bitcoin, Ethereum, and other cryptocurrencies", "profileMenu": {"guide": "Guide", "logo": "Logo", "settings": "Settings", "integration": "Integration", "components": "Components"}, "loadingMoreItems": "Loading more items...", "history": {"tabs": {"imagen": "Imagen", "video": "Video", "speech": "Speech", "music": "Music", "history": "History"}}, "orders": {"title": "Order History", "description": "View your transaction and payment history", "orderId": "Order ID", "amount": "Amount", "credits": "Credits", "quantity": "Quantity", "platform": "Platform", "externalId": "Transaction ID", "status": {"completed": "Completed", "success": "Success", "paid": "Paid", "pending": "Pending", "processing": "Processing", "failed": "Failed", "cancelled": "Cancelled", "error": "Error"}, "empty": {"title": "No orders yet", "description": "You haven't made any orders yet. Buy credits to start using our services.", "action": "Buy Credits"}, "endOfList": "You've seen all orders", "errors": {"fetchFailed": "Failed to load order history. Please try again."}, "meta": {"title": "Order History - Imagen AI", "description": "View your transaction and payment history on Imagen AI"}}, "promptLabel": "Prompt:", "videoExamples": "Video Examples", "videoExamplesDescription": "Explore these video examples with their prompts and settings. Click on any 'Use This Prompt' button to copy the prompt to your input field.", "useThisPrompt": "Use This Prompt", "model": "Model", "duration": "Duration", "videoTypeSelection": "Select Video Type", "videoTypes": {"selectVideoType": "Select Video Type", "searchPlaceholder": "Search video types...", "chooseType": "Choose Type", "examples": "Examples", "selectTypeToSeeExamples": "Select a type to see examples", "advertisingMarketing": {"title": "Advertising & Marketing", "description": "Promote products/services, run Facebook, TikTok, YouTube ads, introduce brands or projects, optimize video marketing production costs"}, "socialMediaContent": {"title": "Social Media Content (Short-form Content)", "description": "Short videos for <PERSON>ik<PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, storytelling videos, reviews, AI voiceovers, convert text or blogs to videos"}, "personalEntertainment": {"title": "Personal Creative Content / Entertainment", "description": "Comedy videos, parodies, memes, storytelling videos, animation, fan-made content, AI-powered short films"}, "educationTraining": {"title": "Education & Training", "description": "Teaching videos, e-learning, courses, skill tutorials, knowledge sharing, presentations with AI avatar instructors"}, "corporateCommunication": {"title": "Internal Communications & Corporate", "description": "Onboarding videos, process guides, internal video announcements, rapid AI-powered employee training"}, "personalizedVideos": {"title": "Personalized Videos", "description": "Birthday wishes, anniversary videos, personalized customer videos (email marketing, customer care), recruitment videos for specific candidates"}, "timeCostSavings": {"title": "Time & Cost Savings in Production", "description": "No need for actors, studios, post-production teams, perfect for individuals/small teams creating content quickly, automated video production workflows"}}, "notifications": {"title": "Notifications", "description": "Your recent notifications and updates", "totalCount": "{count} notifications", "markAllRead": "Mark all as read", "loadMore": "Load more", "close": "Close", "empty": {"title": "No notifications", "description": "You're all caught up! No new notifications to show."}, "error": {"title": "Error loading notifications"}, "time": {"justNow": "Just now", "minutesAgo": "{minutes}m ago", "hoursAgo": "{hours}h ago", "yesterday": "Yesterday"}, "status": {"processing": {"title": "Processing", "description": "Your request is being processed"}, "success": {"title": "Completed", "description": "Successfully completed"}, "failed": {"title": "Failed", "description": "An error occurred during processing"}, "warning": {"title": "Warning", "description": "Completed with warnings"}, "pending": {"title": "Pending", "description": "Waiting to be processed"}, "cancelled": {"title": "Cancelled", "description": "Request was cancelled"}}, "types": {"video_1": {"title": "Video Generation Pending", "description": "Video generation is waiting to be processed"}, "video_2": {"title": "Video Generation Complete", "description": "Video has been generated successfully"}, "video_3": {"title": "Video Generation Failed", "description": "Video generation failed"}, "image_1": {"title": "Image Generation Pending", "description": "Image generation is waiting to be processed"}, "image_2": {"title": "Image Generation Complete", "description": "Image has been generated successfully"}, "image_3": {"title": "Image Generation Failed", "description": "Image generation failed"}, "tts_history_1": {"title": "Audio Generation Pending", "description": "Text-to-speech is waiting to be processed"}, "tts_history_2": {"title": "Audio Generation Complete", "description": "Text-to-speech audio has been generated successfully"}, "tts_history_3": {"title": "Audio Generation Failed", "description": "Text-to-speech generation failed"}, "voice_training_1": {"title": "Voice Training Pending", "description": "Voice training is waiting to be processed"}, "voice_training_2": {"title": "Voice Training Complete", "description": "Custom voice model training has finished successfully"}, "voice_training_3": {"title": "Voice Training Failed", "description": "Voice training failed"}, "music_1": {"title": "Music Generation Pending", "description": "Music generation is waiting to be processed"}, "music_2": {"title": "Music Generation Complete", "description": "AI music has been generated successfully"}, "music_3": {"title": "Music Generation Failed", "description": "Music generation failed"}, "speech_1": {"title": "Speech Generation Pending", "description": "Your speech generation request is waiting to be processed"}, "speech_2": {"title": "Speech Generation Complete", "description": "Your speech has been generated successfully"}, "speech_3": {"title": "Speech Generation Failed", "description": "Your speech generation failed. Please try again"}, "default": {"title": "Notification", "description": "You have a new notification"}}}, "footer": {"privacy": "Privacy", "terms": "Terms", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "textToSpeechOpenAI": "Text To Speech OpenAI", "doctransGPT": "DoctransGPT", "nuxtUIOnDiscord": "Nuxt UI on Discord", "youtube": "Youtube"}, "historyFilter": {"all": "All", "imagen": "Imagen", "videoGen": "Video Gen", "speechGen": "Speech Gen"}, "historyPages": {"imagenDescription": "Browse your AI-generated images and artwork", "musicDescription": "Browse your AI-generated music and audio content", "speechDescription": "Browse your AI-generated speech and voice content", "videoDescription": "Browse your AI-generated videos and animations", "imagenBreadcrumb": "Imagen", "musicBreadcrumb": "Music", "speechBreadcrumb": "Speech", "videoBreadcrumb": "Video Generation", "endOfImagesHistory": "You've reached the end of the images history", "endOfMusicHistory": "You've reached the end of the music history", "endOfSpeechHistory": "You've reached the end of the speech history", "endOfVideoHistory": "You've reached the end of the video history", "noVideosFound": "No videos found", "noVideosFoundDescription": "Start generating videos to see them here.", "backToLibrary": "Back to Library", "errorLoadingVideo": "Error Loading Video", "loadingVideoDetails": "Loading video details...", "videoDetails": "Video Details", "videoInformation": "Video Information", "videoNotFound": "The video you are looking for could not be found or loaded.", "aiContentLibraryTitle": "AI Content Library", "aiContentLibraryDescription": "Browse and manage your AI-generated content across different categories"}, "demo": {"notifications": {"title": "Notification Types & Status Demo", "description": "Examples of different notification types with various status states", "statusLegend": "Status Legend"}, "speechVoiceSelect": {"title": "Speech Voice Select Demo", "description": "Demonstrating the reusable BaseSpeechVoiceSelectModal component with modelValue props"}}, "settings": "Settings", "userMenu": {"profile": "Profile", "buyCredits": "Buy Credits", "settings": "Settings", "api": "API", "logout": "Logout", "greeting": "Hi, {name}"}, "integration": {"title": "Integration", "subtitle": "Manage your API keys and integration settings", "apiKeys": "API Keys", "apiKeysDescription": "Manage your API keys for programmatic access", "webhook": "Webhook", "webhookDescription": "Configure webhook URL for notifications"}, "apiKeys": {"title": "API Keys", "subtitle": "Manage your API keys for programmatic access", "create": "Create API Key", "createNew": "Create New API Key", "createFirst": "Create First API Key", "name": "Name", "nameDescription": "Give your API key a descriptive name", "namePlaceholder": "e.g., My App API Key", "nameRequired": "API key name is required", "createdAt": "Created", "noKeys": "No API Keys", "noKeysDescription": "Create your first API key to get started with programmatic access", "created": "API key created successfully", "createError": "Failed to create API key", "deleted": "API key deleted successfully", "deleteError": "Failed to delete API key", "deleteConfirm": "Delete API Key", "deleteWarning": "Are you sure you want to delete this API key? This action cannot be undone.", "copied": "API key copied to clipboard", "copyError": "Failed to copy API key"}, "webhook": {"title": "Webhook Configuration", "subtitle": "Configure webhook URL for real-time notifications", "configuration": "Webhook URL", "currentUrl": "Current Webhook URL", "currentUrlDescription": "This URL will receive POST requests for webhook events", "notConfigured": "No webhook URL configured", "url": "Webhook URL", "urlDescription": "Enter the URL where you want to receive webhook notifications", "urlPlaceholder": "https://your-domain.com/webhook", "urlRequired": "Please enter a webhook URL first", "invalidUrl": "Please enter a valid URL", "saved": "Webhook URL saved successfully", "saveError": "Failed to save webhook URL", "test": "Test", "testSent": "Test Sent", "testDescription": "Test webhook sent successfully", "information": "Webhook Information", "howItWorks": "How it works", "description": "When configured, we will send HTTP POST requests to your webhook URL whenever certain events occur in your account.", "events": "Webhook Events", "imageGenerated": "Image generation completed", "imageGenerationFailed": "Image generation failed", "creditUpdated": "Credit balance updated", "payloadFormat": "Payload Format", "payloadDescription": "Webhook requests will be sent as JSON with the following structure:", "security": "Security", "securityDescription": "We recommend using HTTPS URLs and implementing signature verification to ensure webhook authenticity."}, "error": {"general": "Error", "validation": "Validation Error", "required": "Required field"}, "success": {"saved": "Saved successfully", "created": "Created successfully", "deleted": "Deleted successfully", "copied": "Copied to clipboard"}, "models": {"imagen3": "Imagen 3", "gemini2Flash": "Gemini 2.0 Flash", "gemini25Flash": "Gemini 2.5 Flash", "sunoAI": "Suno AI", "udio": "Udio", "veo2": "Veo 2", "veo3": "Veo 3", "imagen4": "Imagen 4"}, "formats": {"mp3": "MP3", "wav": "WAV"}, "channels": {"mono": "Mono", "stereo": "Stereo"}, "options": {"allow": "Allow", "dontAllow": "Don't Allow", "voices": "Voices", "pickVoice": "Pick a voice"}, "voiceTypes": {"systemVoices": "System Voices", "customVoices": "Custom Voices", "premiumVoices": "Premium Voices", "userVoices": "User Voices"}, "aspectRatio": "Aspect Ratio", "Image Reference": "Image Reference", "Person Generation": "Person Generation", "safety_filter_level": "Safety Filter Level", "used_credit": "Used Credit", "downloadImage": "Download Image", "noImageAvailable": "No image available", "enhancePrompt": "Enhance Prompt", "addImages": "Add Images", "generateVideo": "Generate Video", "happy": "Happy", "sad": "Sad", "angry": "Angry", "excited": "Excited", "laughing": "Laughing", "crying": "Crying", "calm": "Calm", "serious": "Serious", "frustrated": "Frustrated", "hopeful": "Hopeful", "narrative": "Narrative", "kids' storytelling": "Kids' Storytelling", "audiobook": "Audiobook", "poetic": "Poetic", "mysterious": "Mysterious", "inspirational": "Inspirational", "surprised": "Surprised", "confident": "Confident", "romantic": "Romantic", "scared": "Scared", "trailer voice": "Trailer Voice", "advertising": "Advertising", "speech": "Speech", "documentary": "Documentary", "newsreader": "Newsreader", "weather report": "Weather Report", "game commentary": "Game Commentary", "interactive": "Interactive", "customer support": "Customer Support", "playful": "Playful", "tired": "Tired", "sarcastic": "Sarcastic", "disgusted": "Disgusted", "whispering": "Whispering", "persuasive": "Persuasive", "nostalgic": "Nostalgic", "meditative": "Meditative", "announcement": "Announcement", "professional pitch": "Professional Pitch", "casual": "Casual", "exciting trailer": "Exciting Trailer", "dramatic": "Dramatic", "corporate": "Corporate", "tech enthusiast": "Tech Enthusiast", "youthful": "Youthful", "calming reassurance": "Calming Reassurance", "heroic": "Heroic", "festive": "Festive", "urgent": "<PERSON><PERSON>", "motivational": "Motivational", "friendly": "Friendly", "energetic": "Energetic", "serene": "<PERSON><PERSON>", "bold": "Bold", "charming": "<PERSON><PERSON>", "monotone": "Monotone", "questioning": "Questioning", "directive": "Directive", "dreamy": "<PERSON>y", "epic": "Epic", "lyrical": "Lyrical", "mystical": "Mystical", "melancholy": "<PERSON><PERSON><PERSON><PERSON>", "cheerful": "Cheerful", "eerie": "Eerie", "flirtatious": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "thoughtful": "Thoughtful", "cinematic": "Cinematic", "humorous": "Humorous", "instructional": "Instructional", "conversational": "Conversational", "apologetic": "Apologetic", "excuse-making": "Excuse-making", "encouraging": "Encouraging", "neutral": "Neutral", "authoritative": "Authoritative", "sarcastic cheerful": "Sarcastic Cheerful", "reassuring": "Reassuring", "formal": "Formal", "anguished": "Anguished", "giggling": "Giggling", "exaggerated": "Exaggerated", "cold": "Cold", "hot-tempered": "Hot-tempered", "grateful": "Grateful", "regretful": "Regretful", "provocative": "Provocative", "triumphant": "Triumphant", "vengeful": "Vengeful", "heroic narration": "Heroic Narration", "villainous": "Villainous", "hypnotic": "Hypnotic", "desperate": "Desperate", "lamenting": "Lamenting", "celebratory": "Celebratory", "teasing": "Teasing", "exhausted": "Exhausted", "questioning suspicious": "Questioning Suspicious", "optimistic": "Optimistic", "bright, gentle voice, expressing excitement.": "Bright, gentle voice, expressing excitement.", "low, slow voice, conveying deep emotions.": "Low, slow voice, conveying deep emotions.", "sharp, exaggerated voice, expressing frustration.": "Sharp, exaggerated voice, expressing frustration.", "fast, lively voice, full of enthusiasm.": "Fast, lively voice, full of enthusiasm.", "interrupted, joyful voice, interspersed with laughter.": "Interrupted, joyful voice, interspersed with laughter.", "shaky, low voice, expressing pain.": "Shaky, low voice, expressing pain.", "gentle, steady voice, providing reassurance.": "Gentle, steady voice, providing reassurance.", "mature, clear voice, suitable for formal content.": "Mature, clear voice, suitable for formal content.", "weary, slightly irritated voice.": "Weary, slightly irritated voice.", "bright voice, conveying positivity and hope.": "Bright voice, conveying positivity and hope.", "natural, gentle voice with a slow rhythm.": "Natural, gentle voice with a slow rhythm.", "lively, engaging voice, captivating for children.": "Lively, engaging voice, captivating for children.", "even, slow voice, emphasizing content meaning.": "Even, slow voice, emphasizing content meaning.", "rhythmic, emotional voice, conveying subtlety.": "Rhythmic, emotional voice, conveying subtlety.", "low, slow voice, evoking curiosity.": "Low, slow voice, evoking curiosity.", "strong, passionate voice, driving action.": "Strong, passionate voice, driving action.", "high, interrupted voice, expressing astonishment.": "High, interrupted voice, expressing astonishment.", "firm, powerful voice, persuasive and assuring.": "Firm, powerful voice, persuasive and assuring.", "sweet, gentle voice, suitable for emotional content.": "Sweet, gentle voice, suitable for emotional content.", "shaky, interrupted voice, conveying anxiety.": "Shaky, interrupted voice, conveying anxiety.", "deep, strong voice with emphasis, creating suspense.": "Deep, strong voice with emphasis, creating suspense.", "engaging, lively voice, emphasizing product benefits.": "Engaging, lively voice, emphasizing product benefits.", "formal, clear voice with focus on key points.": "Formal, clear voice with focus on key points.", "calm, profound voice, delivering authenticity.": "Calm, profound voice, delivering authenticity.", "standard, neutral voice, clear and precise.": "Standard, neutral voice, clear and precise.", "bright, neutral voice, suitable for concise updates.": "Bright, neutral voice, suitable for concise updates.", "fast, lively voice, stimulating excitement.": "Fast, lively voice, stimulating excitement.", "friendly, approachable voice, encouraging engagement.": "Friendly, approachable voice, encouraging engagement.", "empathetic, gentle voice, easy to connect with.": "Empathetic, gentle voice, easy to connect with.", "clear voice, emphasizing questions and answers.": "Clear voice, emphasizing questions and answers.", "cheerful, playful voice with a hint of mischief.": "Cheerful, playful voice with a hint of mischief.", "slow, soft voice lacking energy.": "Slow, soft voice lacking energy.", "ironic, sharp voice, sometimes humorous.": "Ironic, sharp voice, sometimes humorous.", "cold voice, clearly expressing discomfort.": "Cold voice, clearly expressing discomfort.", "soft, mysterious voice, creating intimacy.": "Soft, mysterious voice, creating intimacy.", "emotional voice, convincing the listener to act.": "Emotional voice, convincing the listener to act.", "gentle voice, evoking feelings of reminiscence.": "Gentle voice, evoking feelings of reminiscence.", "even, relaxing voice, suitable for mindfulness.": "Even, relaxing voice, suitable for mindfulness.", "clear voice, emphasizing key words.": "Clear voice, emphasizing key words.", "confident, clear voice, ideal for business presentations.": "Confident, clear voice, ideal for business presentations.", "natural, friendly voice, as if talking to a friend.": "Natural, friendly voice, as if talking to a friend.", "fast, powerful voice, creating tension and excitement.": "Fast, powerful voice, creating tension and excitement.", "emphasized, suspenseful voice, creating intensity.": "Emphasized, suspenseful voice, creating intensity.", "professional, formal voice, suitable for business content.": "Professional, formal voice, suitable for business content.", "energetic, lively voice, introducing new technologies.": "Energetic, lively voice, introducing new technologies.", "vibrant, cheerful voice, appealing to younger audiences.": "Vibrant, cheerful voice, appealing to younger audiences.", "gentle, empathetic voice, easing concerns.": "Gentle, empathetic voice, easing concerns.", "strong, decisive voice, full of inspiration.": "Strong, decisive voice, full of inspiration.", "bright, excited voice, suitable for celebrations.": "Bright, excited voice, suitable for celebrations.", "fast, strong voice, emphasizing urgency.": "Fast, strong voice, emphasizing urgency.", "passionate, inspiring voice, encouraging action.": "Passionate, inspiring voice, encouraging action.", "warm, approachable voice, fostering connection.": "Warm, approachable voice, fostering connection.", "fast, powerful voice, brimming with enthusiasm.": "Fast, powerful voice, brimming with enthusiasm.", "slow, gentle voice, evoking peace and tranquility.": "Slow, gentle voice, evoking peace and tranquility.", "firm, assertive voice, exuding confidence.": "Firm, assertive voice, exuding confidence.", "warm, captivating voice, leaving a strong impression.": "Warm, captivating voice, leaving a strong impression.", "flat, unvaried voice, conveying neutrality or irony.": "Flat, unvaried voice, conveying neutrality or irony.", "curious voice, emphasizing questions.": "Curious voice, emphasizing questions.", "firm, clear voice, guiding the listener step-by-step.": "Firm, clear voice, guiding the listener step-by-step.", "gentle, slow voice, evoking a floating sensation.": "Gentle, slow voice, evoking a floating sensation.", "deep, resonant voice, emphasizing grandeur.": "Deep, resonant voice, emphasizing grandeur.", "soft, melodic voice, similar to singing.": "Soft, melodic voice, similar to singing.", "low, drawn-out voice, evoking mystery.": "Low, drawn-out voice, evoking mystery.", "slow, low voice, conveying deep sadness.": "Slow, low voice, conveying deep sadness.", "bright, energetic voice, full of positivity.": "Bright, energetic voice, full of positivity.", "low, whispery voice, evoking fear or strangeness.": "Low, whispery voice, evoking fear or strangeness.", "sweet, teasing voice, full of allure.": "Sweet, teasing voice, full of allure.", "slow, reflective voice, full of contemplation.": "Slow, reflective voice, full of contemplation.", "resonant, emphasized voice, creating a movie-like effect.": "Resonant, emphasized voice, creating a movie-like effect.", "lighthearted, cheerful voice, sometimes exaggerated.": "Lighthearted, cheerful voice, sometimes exaggerated.", "clear, slow voice, guiding the listener step-by-step.": "Clear, slow voice, guiding the listener step-by-step.", "natural voice, as if chatting with the listener.": "Natural voice, as if chatting with the listener.", "soft, sincere voice, expressing regret.": "Soft, sincere voice, expressing regret.", "hesitant, uncertain voice, sometimes awkward.": "Hesitant, uncertain voice, sometimes awkward.", "warm voice, providing motivation and support.": "Warm voice, providing motivation and support.", "even voice, free of emotional bias.": "Even voice, free of emotional bias.", "strong, powerful voice, exuding credibility.": "Strong, powerful voice, exuding credibility.", "cheerful voice with an undertone of mockery.": "Cheerful voice with an undertone of mockery.", "gentle, empathetic voice, providing comfort.": "Gentle, empathetic voice, providing comfort.", "clear, polite voice, suited for formal occasions.": "Clear, polite voice, suited for formal occasions.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON>, shaky voice, expressing distress.", "interrupted voice, mixed with light laughter.": "Interrupted voice, mixed with light laughter.", "loud, emphasized voice, often humorous.": "Loud, emphasized voice, often humorous.", "flat, unemotional voice, conveying detachment.": "Flat, unemotional voice, conveying detachment.", "fast, sharp voice, sometimes out of control.": "Fast, sharp voice, sometimes out of control.", "warm, sincere voice, expressing appreciation.": "Warm, sincere voice, expressing appreciation.", "low, subdued voice, full of remorse.": "Low, subdued voice, full of remorse.", "challenging, strong voice, full of insinuation.": "Challenging, strong voice, full of insinuation.", "loud, powerful voice, full of victory.": "Loud, powerful voice, full of victory.", "low, cold voice, expressing determination for revenge.": "Low, cold voice, expressing determination for revenge.", "strong, inspiring voice, emphasizing heroic deeds.": "Strong, inspiring voice, emphasizing heroic deeds.", "low, drawn-out voice, full of scheming.": "Low, drawn-out voice, full of scheming.", "even, repetitive voice, drawing the listener in.": "Even, repetitive voice, drawing the listener in.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON>, shaky voice, expressing hopelessness.", "low, sorrowful voice, as if mourning.": "Low, sorrowful voice, as if mourning.", "excited, joyful voice, full of festive spirit.": "Excited, joyful voice, full of festive spirit.", "light, playful voice, sometimes mockingly.": "Light, playful voice, sometimes mockingly.", "weak, broken voice, expressing extreme fatigue.": "Weak, broken voice, expressing extreme fatigue.", "slow, emphasized voice, full of suspicion.": "Slow, emphasized voice, full of suspicion.", "bright, hopeful voice, creating positivity.": "Bright, hopeful voice, creating positivity.", "Your audio will be processed with the latest stable model.": "Your audio will be processed with the latest stable model.", "Your audio will be processed with the latest beta model.": "Your audio will be processed with the latest beta model.", "You don't have any saved prompts yet.": "You don't have any saved prompts yet.", "Commercial Use": "Commercial Use", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.": "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.", "Other people’s privacy": "Other people’s privacy", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.", "{price}$ per credit": "{price}$ per credit", "Pricing": "Pricing", "Simple and flexible. Only pay for what you use.": "Simple and flexible. Only pay for what you use.", "Pay as you go": "Pay as you go", "Flexible": "Flexible", "Input characters": "Input characters", "Audio model": "Audio model", "Credits": "Credits", "Cost": "Cost", "HD quality voices": "HD quality voices", "Advanced model": "Advanced model", "Buy now": "Buy now", "Paste your text to calculate": "Paste your text to calculate", "Paste your text here...": "Paste your text here...", "Calculate": "Calculate", "Estimate your cost by drag the slider below or": "Estimate your cost by drag the slider below or", "calming": "Calming", "customer": "Customer", "exciting": "exciting", "excuse": "Excuse", "game": "Game", "hot": "Hot", "kids": "Kids", "professional": "Professional", "tech": "Tech", "trailer": "Trailer", "weather": "Weather", "No thumbnail available": "No thumbnail available", "Debit or Credit Card": "Debit or Credit Card", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express and more", "Top up now": "Top up now", "noVideoAvailable": "No video available", "tts-document": "File to Speech", "assignVoicesToSpeakers": "Assign Voices to Speakers", "speakers": "Speakers", "addSpeaker": "Add Speaker", "noVoiceAssigned": "No voice assigned", "noSpeakersAdded": "No speakers added yet", "assignVoiceToSpeaker": "Assign voice to {speaker}", "assigned": "Assigned", "assign": "Assign", "editSpeaker": "Edit Speaker", "speakerName": "Speaker Name", "enterSpeakerName": "Enter speaker name", "save": "Save", "speaker": "Speaker", "assignVoices": "Assign Voices", "speakersWithVoices": "{assigned}/{total} speakers have voices", "dialogs": "Dialogs", "addDialog": "Add Dialog", "enterDialogText": "Enter dialog text...", "selectSpeaker": "Select Speaker", "generateDialogSpeech": "Generate Dialog Speech", "voice 1": "Voice 1", "voice 2": "Voice 2"}